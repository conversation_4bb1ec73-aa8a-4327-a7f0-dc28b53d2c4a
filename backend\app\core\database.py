"""
Database configuration and session management
إعدادات قاعدة البيانات وإدارة الجلسات
"""

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from typing import AsyncGenerator
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

# Create async engine
engine = create_async_engine(
    settings.database_url_async,
    echo=settings.DEBUG,
    pool_pre_ping=True,
    pool_recycle=300,
    pool_size=20,
    max_overflow=30
)

# Create async session factory
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False
)

# Create sync engine for Alembic migrations
sync_engine = create_engine(
    settings.DATABASE_URL,
    echo=settings.DEBUG,
    pool_pre_ping=True,
    pool_recycle=300
)

# Create sync session factory
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=sync_engine
)

# Create declarative base
Base = declarative_base()


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency to get database session
    تبعية للحصول على جلسة قاعدة البيانات
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception as e:
            await session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            await session.close()


def get_sync_db():
    """
    Get synchronous database session
    الحصول على جلسة قاعدة بيانات متزامنة
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        db.rollback()
        logger.error(f"Sync database session error: {e}")
        raise
    finally:
        db.close()


async def init_db():
    """
    Initialize database tables
    تهيئة جداول قاعدة البيانات
    """
    async with engine.begin() as conn:
        # Import all models to ensure they are registered
        from app.models import user, account, template, campaign, session
        
        # Create all tables
        await conn.run_sync(Base.metadata.create_all)
        logger.info("Database tables created successfully")
        logger.info("تم إنشاء جداول قاعدة البيانات بنجاح")


async def close_db():
    """
    Close database connections
    إغلاق اتصالات قاعدة البيانات
    """
    await engine.dispose()
    logger.info("Database connections closed")
    logger.info("تم إغلاق اتصالات قاعدة البيانات")


# Database health check
async def check_db_health() -> bool:
    """
    Check database connectivity
    فحص اتصال قاعدة البيانات
    """
    try:
        async with AsyncSessionLocal() as session:
            await session.execute("SELECT 1")
            return True
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return False
