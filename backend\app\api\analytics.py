"""
Analytics API endpoints
نقاط نهاية API للتحليلات
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import logging

from app.core.database import get_db
from app.core.i18n import _, Language
from app.models.user import User
from app.models.account import FacebookAccount
from app.models.template import Template
from app.models.campaign import Campaign
from app.models.post import Post
from app.api.auth import get_current_active_user

logger = logging.getLogger(__name__)

# Create router
analytics_router = APIRouter()


# Pydantic models
class DashboardStats(BaseModel):
    total_accounts: int
    active_accounts: int
    total_templates: int
    active_campaigns: int
    total_posts: int
    successful_posts: int
    failed_posts: int
    success_rate: float
    posts_today: int
    posts_this_week: int
    posts_this_month: int


class AccountAnalytics(BaseModel):
    account_id: str
    account_name: str
    email: str
    status: str
    total_posts: int
    successful_posts: int
    failed_posts: int
    success_rate: float
    health_score: int
    risk_level: str
    last_activity: Optional[datetime]


class TemplateAnalytics(BaseModel):
    template_id: str
    template_name: str
    category: str
    times_used: int
    successful_posts: int
    failed_posts: int
    success_rate: float
    last_used: Optional[datetime]


class CampaignAnalytics(BaseModel):
    campaign_id: str
    campaign_name: str
    status: str
    total_posts_planned: int
    total_posts_completed: int
    successful_posts: int
    failed_posts: int
    progress_percentage: float
    success_rate: float
    started_at: Optional[datetime]
    estimated_completion: Optional[datetime]


class PostsOverTime(BaseModel):
    date: str
    total_posts: int
    successful_posts: int
    failed_posts: int


class CategoryPerformance(BaseModel):
    category: str
    total_posts: int
    successful_posts: int
    success_rate: float
    avg_views: float
    avg_messages: float


@analytics_router.get("/dashboard", response_model=DashboardStats)
async def get_dashboard_stats(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get dashboard statistics
    الحصول على إحصائيات لوحة التحكم
    """
    try:
        # Get account stats
        accounts_result = await db.execute(
            select(func.count(FacebookAccount.id))
            .where(FacebookAccount.user_id == current_user.id)
        )
        total_accounts = accounts_result.scalar() or 0
        
        active_accounts_result = await db.execute(
            select(func.count(FacebookAccount.id))
            .where(
                and_(
                    FacebookAccount.user_id == current_user.id,
                    FacebookAccount.status == "active"
                )
            )
        )
        active_accounts = active_accounts_result.scalar() or 0
        
        # Get template stats
        templates_result = await db.execute(
            select(func.count(Template.id))
            .where(Template.user_id == current_user.id)
        )
        total_templates = templates_result.scalar() or 0
        
        # Get campaign stats
        campaigns_result = await db.execute(
            select(func.count(Campaign.id))
            .where(
                and_(
                    Campaign.user_id == current_user.id,
                    Campaign.status.in_(["running", "scheduled"])
                )
            )
        )
        active_campaigns = campaigns_result.scalar() or 0
        
        # Get post stats
        posts_stats = await db.execute(
            select(
                func.count(Post.id).label("total"),
                func.sum(func.case((Post.status == "posted", 1), else_=0)).label("successful"),
                func.sum(func.case((Post.status == "failed", 1), else_=0)).label("failed")
            )
            .select_from(Post)
            .join(FacebookAccount, Post.facebook_account_id == FacebookAccount.id)
            .where(FacebookAccount.user_id == current_user.id)
        )
        post_stats = posts_stats.first()
        
        total_posts = post_stats.total or 0
        successful_posts = post_stats.successful or 0
        failed_posts = post_stats.failed or 0
        success_rate = (successful_posts / total_posts * 100) if total_posts > 0 else 0
        
        # Get time-based stats
        today = datetime.utcnow().date()
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)
        
        posts_today_result = await db.execute(
            select(func.count(Post.id))
            .select_from(Post)
            .join(FacebookAccount, Post.facebook_account_id == FacebookAccount.id)
            .where(
                and_(
                    FacebookAccount.user_id == current_user.id,
                    func.date(Post.created_at) == today
                )
            )
        )
        posts_today = posts_today_result.scalar() or 0
        
        posts_week_result = await db.execute(
            select(func.count(Post.id))
            .select_from(Post)
            .join(FacebookAccount, Post.facebook_account_id == FacebookAccount.id)
            .where(
                and_(
                    FacebookAccount.user_id == current_user.id,
                    func.date(Post.created_at) >= week_ago
                )
            )
        )
        posts_this_week = posts_week_result.scalar() or 0
        
        posts_month_result = await db.execute(
            select(func.count(Post.id))
            .select_from(Post)
            .join(FacebookAccount, Post.facebook_account_id == FacebookAccount.id)
            .where(
                and_(
                    FacebookAccount.user_id == current_user.id,
                    func.date(Post.created_at) >= month_ago
                )
            )
        )
        posts_this_month = posts_month_result.scalar() or 0
        
        return DashboardStats(
            total_accounts=total_accounts,
            active_accounts=active_accounts,
            total_templates=total_templates,
            active_campaigns=active_campaigns,
            total_posts=total_posts,
            successful_posts=successful_posts,
            failed_posts=failed_posts,
            success_rate=round(success_rate, 2),
            posts_today=posts_today,
            posts_this_week=posts_this_week,
            posts_this_month=posts_this_month
        )
        
    except Exception as e:
        logger.error(f"Error getting dashboard stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )


@analytics_router.get("/accounts", response_model=List[AccountAnalytics])
async def get_accounts_analytics(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get accounts analytics
    الحصول على تحليلات الحسابات
    """
    try:
        result = await db.execute(
            select(FacebookAccount)
            .where(FacebookAccount.user_id == current_user.id)
            .order_by(FacebookAccount.health_score.desc())
        )
        accounts = result.scalars().all()
        
        analytics = []
        for account in accounts:
            analytics.append(AccountAnalytics(
                account_id=str(account.id),
                account_name=account.account_name,
                email=account.email,
                status=account.status,
                total_posts=account.total_posts,
                successful_posts=account.successful_posts,
                failed_posts=account.failed_posts,
                success_rate=round(account.success_rate, 2),
                health_score=account.health_score,
                risk_level=account.risk_level,
                last_activity=account.last_activity_at
            ))
        
        return analytics
        
    except Exception as e:
        logger.error(f"Error getting accounts analytics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )


@analytics_router.get("/templates", response_model=List[TemplateAnalytics])
async def get_templates_analytics(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get templates analytics
    الحصول على تحليلات القوالب
    """
    try:
        result = await db.execute(
            select(Template)
            .where(Template.user_id == current_user.id)
            .order_by(Template.success_rate.desc())
        )
        templates = result.scalars().all()
        
        analytics = []
        for template in templates:
            analytics.append(TemplateAnalytics(
                template_id=str(template.id),
                template_name=template.name,
                category=template.category,
                times_used=template.times_used,
                successful_posts=template.successful_posts,
                failed_posts=template.failed_posts,
                success_rate=round(template.success_rate, 2),
                last_used=template.last_used_at
            ))
        
        return analytics
        
    except Exception as e:
        logger.error(f"Error getting templates analytics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )


@analytics_router.get("/campaigns", response_model=List[CampaignAnalytics])
async def get_campaigns_analytics(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get campaigns analytics
    الحصول على تحليلات الحملات
    """
    try:
        result = await db.execute(
            select(Campaign)
            .where(Campaign.user_id == current_user.id)
            .order_by(Campaign.created_at.desc())
        )
        campaigns = result.scalars().all()
        
        analytics = []
        for campaign in campaigns:
            analytics.append(CampaignAnalytics(
                campaign_id=str(campaign.id),
                campaign_name=campaign.name,
                status=campaign.status,
                total_posts_planned=campaign.total_posts_planned,
                total_posts_completed=campaign.total_posts_completed,
                successful_posts=campaign.successful_posts,
                failed_posts=campaign.failed_posts,
                progress_percentage=round(campaign.progress_percentage, 2),
                success_rate=round(campaign.success_rate, 2),
                started_at=campaign.started_at,
                estimated_completion=campaign.estimated_completion_time
            ))
        
        return analytics
        
    except Exception as e:
        logger.error(f"Error getting campaigns analytics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )


@analytics_router.get("/posts-over-time", response_model=List[PostsOverTime])
async def get_posts_over_time(
    days: int = Query(30, ge=1, le=365),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get posts over time analytics
    الحصول على تحليلات المنشورات عبر الزمن
    """
    try:
        start_date = datetime.utcnow().date() - timedelta(days=days)
        
        result = await db.execute(
            select(
                func.date(Post.created_at).label("date"),
                func.count(Post.id).label("total"),
                func.sum(func.case((Post.status == "posted", 1), else_=0)).label("successful"),
                func.sum(func.case((Post.status == "failed", 1), else_=0)).label("failed")
            )
            .select_from(Post)
            .join(FacebookAccount, Post.facebook_account_id == FacebookAccount.id)
            .where(
                and_(
                    FacebookAccount.user_id == current_user.id,
                    func.date(Post.created_at) >= start_date
                )
            )
            .group_by(func.date(Post.created_at))
            .order_by(func.date(Post.created_at))
        )
        
        data = result.all()
        
        analytics = []
        for row in data:
            analytics.append(PostsOverTime(
                date=row.date.isoformat(),
                total_posts=row.total or 0,
                successful_posts=row.successful or 0,
                failed_posts=row.failed or 0
            ))
        
        return analytics
        
    except Exception as e:
        logger.error(f"Error getting posts over time: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )


@analytics_router.get("/category-performance", response_model=List[CategoryPerformance])
async def get_category_performance(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get category performance analytics
    الحصول على تحليلات أداء الفئات
    """
    try:
        result = await db.execute(
            select(
                Post.category,
                func.count(Post.id).label("total"),
                func.sum(func.case((Post.status == "posted", 1), else_=0)).label("successful"),
                func.avg(Post.views_count).label("avg_views"),
                func.avg(Post.messages_count).label("avg_messages")
            )
            .select_from(Post)
            .join(FacebookAccount, Post.facebook_account_id == FacebookAccount.id)
            .where(FacebookAccount.user_id == current_user.id)
            .group_by(Post.category)
            .order_by(func.count(Post.id).desc())
        )
        
        data = result.all()
        
        analytics = []
        for row in data:
            total = row.total or 0
            successful = row.successful or 0
            success_rate = (successful / total * 100) if total > 0 else 0
            
            analytics.append(CategoryPerformance(
                category=row.category,
                total_posts=total,
                successful_posts=successful,
                success_rate=round(success_rate, 2),
                avg_views=round(row.avg_views or 0, 2),
                avg_messages=round(row.avg_messages or 0, 2)
            ))
        
        return analytics
        
    except Exception as e:
        logger.error(f"Error getting category performance: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )
