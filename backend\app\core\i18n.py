"""
Internationalization support for Arabic and English
دعم التدويل للعربية والإنجليزية
"""

from typing import Dict, Optional
from enum import Enum
import json
import os
from pathlib import Path

from app.core.config import settings


class Language(str, Enum):
    """Supported languages"""
    ENGLISH = "en"
    ARABIC = "ar"


class Direction(str, Enum):
    """Text direction"""
    LTR = "ltr"  # Left to Right
    RTL = "rtl"  # Right to Left


# Language configurations
LANGUAGE_CONFIG = {
    Language.ENGLISH: {
        "name": "English",
        "native_name": "English",
        "direction": Direction.LTR,
        "font_family": "Inter, sans-serif",
        "locale": "en-US"
    },
    Language.ARABIC: {
        "name": "Arabic",
        "native_name": "العربية",
        "direction": Direction.RTL,
        "font_family": "Cairo, sans-serif",
        "locale": "ar-SA"
    }
}

# Translation dictionaries
TRANSLATIONS = {
    Language.ENGLISH: {
        # Authentication
        "auth.login": "Login",
        "auth.logout": "Logout",
        "auth.register": "Register",
        "auth.email": "Email",
        "auth.password": "Password",
        "auth.confirm_password": "Confirm Password",
        "auth.forgot_password": "Forgot Password?",
        "auth.reset_password": "Reset Password",
        "auth.login_success": "Login successful",
        "auth.login_failed": "Login failed",
        "auth.invalid_credentials": "Invalid email or password",
        "auth.account_disabled": "Account is disabled",
        "auth.email_not_verified": "Email not verified",
        
        # Dashboard
        "dashboard.title": "Dashboard",
        "dashboard.welcome": "Welcome to Facebook Marketplace Platform",
        "dashboard.total_accounts": "Total Accounts",
        "dashboard.active_campaigns": "Active Campaigns",
        "dashboard.total_posts": "Total Posts",
        "dashboard.success_rate": "Success Rate",
        
        # Accounts
        "accounts.title": "Facebook Accounts",
        "accounts.add_account": "Add Account",
        "accounts.edit_account": "Edit Account",
        "accounts.delete_account": "Delete Account",
        "accounts.account_name": "Account Name",
        "accounts.email": "Email",
        "accounts.status": "Status",
        "accounts.last_login": "Last Login",
        "accounts.actions": "Actions",
        
        # Templates
        "templates.title": "Product Templates",
        "templates.add_template": "Add Template",
        "templates.edit_template": "Edit Template",
        "templates.template_name": "Template Name",
        "templates.category": "Category",
        "templates.price": "Price",
        "templates.description": "Description",
        
        # Campaigns
        "campaigns.title": "Campaigns",
        "campaigns.add_campaign": "Add Campaign",
        "campaigns.campaign_name": "Campaign Name",
        "campaigns.start_date": "Start Date",
        "campaigns.end_date": "End Date",
        "campaigns.status": "Status",
        "campaigns.posts_count": "Posts Count",
        
        # Common
        "common.save": "Save",
        "common.cancel": "Cancel",
        "common.delete": "Delete",
        "common.edit": "Edit",
        "common.view": "View",
        "common.search": "Search",
        "common.filter": "Filter",
        "common.export": "Export",
        "common.import": "Import",
        "common.loading": "Loading...",
        "common.error": "Error",
        "common.success": "Success",
        "common.warning": "Warning",
        "common.info": "Information",
        "common.yes": "Yes",
        "common.no": "No",
        "common.active": "Active",
        "common.inactive": "Inactive",
        "common.pending": "Pending",
        "common.completed": "Completed",
        "common.failed": "Failed",
        
        # Errors
        "error.not_found": "Not found",
        "error.unauthorized": "Unauthorized",
        "error.forbidden": "Forbidden",
        "error.internal_server": "Internal server error",
        "error.validation": "Validation error",
        "error.network": "Network error",
        
        # Settings
        "settings.title": "Settings",
        "settings.profile": "Profile",
        "settings.security": "Security",
        "settings.notifications": "Notifications",
        "settings.language": "Language",
        "settings.theme": "Theme",
        "settings.timezone": "Timezone",
    },
    
    Language.ARABIC: {
        # Authentication
        "auth.login": "تسجيل الدخول",
        "auth.logout": "تسجيل الخروج",
        "auth.register": "إنشاء حساب",
        "auth.email": "البريد الإلكتروني",
        "auth.password": "كلمة المرور",
        "auth.confirm_password": "تأكيد كلمة المرور",
        "auth.forgot_password": "نسيت كلمة المرور؟",
        "auth.reset_password": "إعادة تعيين كلمة المرور",
        "auth.login_success": "تم تسجيل الدخول بنجاح",
        "auth.login_failed": "فشل تسجيل الدخول",
        "auth.invalid_credentials": "البريد الإلكتروني أو كلمة المرور غير صحيحة",
        "auth.account_disabled": "الحساب معطل",
        "auth.email_not_verified": "البريد الإلكتروني غير مؤكد",
        
        # Dashboard
        "dashboard.title": "لوحة التحكم",
        "dashboard.welcome": "مرحباً بك في منصة فيسبوك ماركت بليس",
        "dashboard.total_accounts": "إجمالي الحسابات",
        "dashboard.active_campaigns": "الحملات النشطة",
        "dashboard.total_posts": "إجمالي المنشورات",
        "dashboard.success_rate": "معدل النجاح",
        
        # Accounts
        "accounts.title": "حسابات فيسبوك",
        "accounts.add_account": "إضافة حساب",
        "accounts.edit_account": "تعديل الحساب",
        "accounts.delete_account": "حذف الحساب",
        "accounts.account_name": "اسم الحساب",
        "accounts.email": "البريد الإلكتروني",
        "accounts.status": "الحالة",
        "accounts.last_login": "آخر تسجيل دخول",
        "accounts.actions": "الإجراءات",
        
        # Templates
        "templates.title": "قوالب المنتجات",
        "templates.add_template": "إضافة قالب",
        "templates.edit_template": "تعديل القالب",
        "templates.template_name": "اسم القالب",
        "templates.category": "الفئة",
        "templates.price": "السعر",
        "templates.description": "الوصف",
        
        # Campaigns
        "campaigns.title": "الحملات",
        "campaigns.add_campaign": "إضافة حملة",
        "campaigns.campaign_name": "اسم الحملة",
        "campaigns.start_date": "تاريخ البداية",
        "campaigns.end_date": "تاريخ النهاية",
        "campaigns.status": "الحالة",
        "campaigns.posts_count": "عدد المنشورات",
        
        # Common
        "common.save": "حفظ",
        "common.cancel": "إلغاء",
        "common.delete": "حذف",
        "common.edit": "تعديل",
        "common.view": "عرض",
        "common.search": "بحث",
        "common.filter": "تصفية",
        "common.export": "تصدير",
        "common.import": "استيراد",
        "common.loading": "جاري التحميل...",
        "common.error": "خطأ",
        "common.success": "نجح",
        "common.warning": "تحذير",
        "common.info": "معلومات",
        "common.yes": "نعم",
        "common.no": "لا",
        "common.active": "نشط",
        "common.inactive": "غير نشط",
        "common.pending": "في الانتظار",
        "common.completed": "مكتمل",
        "common.failed": "فشل",
        
        # Errors
        "error.not_found": "غير موجود",
        "error.unauthorized": "غير مصرح",
        "error.forbidden": "محظور",
        "error.internal_server": "خطأ داخلي في الخادم",
        "error.validation": "خطأ في التحقق",
        "error.network": "خطأ في الشبكة",
        
        # Settings
        "settings.title": "الإعدادات",
        "settings.profile": "الملف الشخصي",
        "settings.security": "الأمان",
        "settings.notifications": "الإشعارات",
        "settings.language": "اللغة",
        "settings.theme": "المظهر",
        "settings.timezone": "المنطقة الزمنية",
    }
}


class I18n:
    """Internationalization class"""
    
    def __init__(self, default_language: Language = Language.ENGLISH):
        self.default_language = default_language
        self.current_language = default_language
    
    def set_language(self, language: Language):
        """Set current language"""
        if language in LANGUAGE_CONFIG:
            self.current_language = language
    
    def get_language_config(self, language: Optional[Language] = None) -> Dict:
        """Get language configuration"""
        lang = language or self.current_language
        return LANGUAGE_CONFIG.get(lang, LANGUAGE_CONFIG[self.default_language])
    
    def translate(self, key: str, language: Optional[Language] = None) -> str:
        """Translate a key to the specified language"""
        lang = language or self.current_language
        translations = TRANSLATIONS.get(lang, TRANSLATIONS[self.default_language])
        return translations.get(key, key)
    
    def t(self, key: str, language: Optional[Language] = None) -> str:
        """Short alias for translate"""
        return self.translate(key, language)
    
    def get_direction(self, language: Optional[Language] = None) -> Direction:
        """Get text direction for language"""
        config = self.get_language_config(language)
        return config["direction"]
    
    def is_rtl(self, language: Optional[Language] = None) -> bool:
        """Check if language is RTL"""
        return self.get_direction(language) == Direction.RTL
    
    def get_font_family(self, language: Optional[Language] = None) -> str:
        """Get font family for language"""
        config = self.get_language_config(language)
        return config["font_family"]
    
    def get_locale(self, language: Optional[Language] = None) -> str:
        """Get locale for language"""
        config = self.get_language_config(language)
        return config["locale"]


# Global i18n instance
i18n = I18n(Language(settings.DEFAULT_LANGUAGE))


def get_i18n() -> I18n:
    """Get i18n instance"""
    return i18n


def _(key: str, language: Optional[Language] = None) -> str:
    """Global translation function"""
    return i18n.translate(key, language)
