"""
Facebook Accounts API endpoints
نقاط نهاية API لحسابات فيسبوك
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from pydantic import BaseModel, EmailStr
from typing import Optional, List
from datetime import datetime
import logging

from app.core.database import get_db
from app.core.security import encrypt_sensitive_data, decrypt_sensitive_data
from app.core.i18n import _, Language
from app.models.user import User
from app.models.account import FacebookAccount, AccountStatus, LoginMethod
from app.api.auth import get_current_active_user

logger = logging.getLogger(__name__)

# Create router
accounts_router = APIRouter()


# Pydantic models
class FacebookAccountCreate(BaseModel):
    account_name: str
    email: EmailStr
    password: Optional[str] = None
    access_token: Optional[str] = None
    login_method: LoginMethod = LoginMethod.EMAIL_PASSWORD
    notes: Optional[str] = None


class FacebookAccountUpdate(BaseModel):
    account_name: Optional[str] = None
    password: Optional[str] = None
    access_token: Optional[str] = None
    notes: Optional[str] = None
    status: Optional[AccountStatus] = None


class FacebookAccountResponse(BaseModel):
    id: str
    account_name: str
    email: str
    username: Optional[str]
    status: str
    login_method: str
    profile_name: Optional[str]
    profile_picture_url: Optional[str]
    is_business_account: bool
    is_verified: bool
    last_login_at: Optional[datetime]
    last_activity_at: Optional[datetime]
    total_posts: int
    successful_posts: int
    failed_posts: int
    health_score: int
    risk_level: str
    marketplace_access: bool
    notes: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class FacebookAccountList(BaseModel):
    accounts: List[FacebookAccountResponse]
    total: int
    page: int
    per_page: int


@accounts_router.get("/", response_model=FacebookAccountList)
async def get_facebook_accounts(
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    status: Optional[AccountStatus] = None,
    search: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get user's Facebook accounts
    الحصول على حسابات فيسبوك للمستخدم
    """
    try:
        # Build query
        query = select(FacebookAccount).where(FacebookAccount.user_id == current_user.id)
        
        # Add filters
        if status:
            query = query.where(FacebookAccount.status == status.value)
        
        if search:
            query = query.where(
                FacebookAccount.account_name.ilike(f"%{search}%") |
                FacebookAccount.email.ilike(f"%{search}%")
            )
        
        # Get total count
        count_result = await db.execute(query)
        total = len(count_result.all())
        
        # Add pagination
        offset = (page - 1) * per_page
        query = query.offset(offset).limit(per_page)
        
        # Execute query
        result = await db.execute(query)
        accounts = result.scalars().all()
        
        # Convert to response models
        account_responses = [
            FacebookAccountResponse.from_orm(account) for account in accounts
        ]
        
        return FacebookAccountList(
            accounts=account_responses,
            total=total,
            page=page,
            per_page=per_page
        )
        
    except Exception as e:
        logger.error(f"Error getting Facebook accounts: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )


@accounts_router.post("/", response_model=FacebookAccountResponse)
async def create_facebook_account(
    account_data: FacebookAccountCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create new Facebook account
    إنشاء حساب فيسبوك جديد
    """
    try:
        # Check if user can add more accounts
        if not current_user.can_add_facebook_account:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=_("accounts.limit_reached", Language(current_user.language))
            )
        
        # Check if account already exists
        result = await db.execute(
            select(FacebookAccount).where(
                and_(
                    FacebookAccount.user_id == current_user.id,
                    FacebookAccount.email == account_data.email
                )
            )
        )
        existing_account = result.scalar_one_or_none()
        
        if existing_account:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=_("accounts.email_already_exists", Language(current_user.language))
            )
        
        # Validate login method and credentials
        if account_data.login_method == LoginMethod.EMAIL_PASSWORD and not account_data.password:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=_("accounts.password_required", Language(current_user.language))
            )
        
        if account_data.login_method == LoginMethod.ACCESS_TOKEN and not account_data.access_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=_("accounts.access_token_required", Language(current_user.language))
            )
        
        # Create new Facebook account
        new_account = FacebookAccount(
            user_id=current_user.id,
            account_name=account_data.account_name,
            email=account_data.email,
            login_method=account_data.login_method.value,
            notes=account_data.notes
        )
        
        # Encrypt and store credentials
        if account_data.password:
            new_account.encrypted_password = encrypt_sensitive_data(account_data.password)
        
        if account_data.access_token:
            new_account.access_token = encrypt_sensitive_data(account_data.access_token)
        
        db.add(new_account)
        
        # Update user's account count
        current_user.increment_facebook_accounts()
        
        await db.commit()
        await db.refresh(new_account)
        
        logger.info(f"New Facebook account created: {new_account.email} for user {current_user.email}")
        
        return FacebookAccountResponse.from_orm(new_account)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating Facebook account: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )


@accounts_router.get("/{account_id}", response_model=FacebookAccountResponse)
async def get_facebook_account(
    account_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get specific Facebook account
    الحصول على حساب فيسبوك محدد
    """
    try:
        result = await db.execute(
            select(FacebookAccount).where(
                and_(
                    FacebookAccount.id == account_id,
                    FacebookAccount.user_id == current_user.id
                )
            )
        )
        account = result.scalar_one_or_none()
        
        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=_("accounts.not_found", Language(current_user.language))
            )
        
        return FacebookAccountResponse.from_orm(account)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting Facebook account: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )


@accounts_router.put("/{account_id}", response_model=FacebookAccountResponse)
async def update_facebook_account(
    account_id: str,
    account_data: FacebookAccountUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update Facebook account
    تحديث حساب فيسبوك
    """
    try:
        result = await db.execute(
            select(FacebookAccount).where(
                and_(
                    FacebookAccount.id == account_id,
                    FacebookAccount.user_id == current_user.id
                )
            )
        )
        account = result.scalar_one_or_none()
        
        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=_("accounts.not_found", Language(current_user.language))
            )
        
        # Update fields
        if account_data.account_name is not None:
            account.account_name = account_data.account_name
        
        if account_data.password is not None:
            account.encrypted_password = encrypt_sensitive_data(account_data.password)
        
        if account_data.access_token is not None:
            account.access_token = encrypt_sensitive_data(account_data.access_token)
        
        if account_data.notes is not None:
            account.notes = account_data.notes
        
        if account_data.status is not None:
            account.status = account_data.status.value
        
        await db.commit()
        await db.refresh(account)
        
        logger.info(f"Facebook account updated: {account.email}")
        
        return FacebookAccountResponse.from_orm(account)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating Facebook account: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )


@accounts_router.delete("/{account_id}")
async def delete_facebook_account(
    account_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete Facebook account
    حذف حساب فيسبوك
    """
    try:
        result = await db.execute(
            select(FacebookAccount).where(
                and_(
                    FacebookAccount.id == account_id,
                    FacebookAccount.user_id == current_user.id
                )
            )
        )
        account = result.scalar_one_or_none()
        
        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=_("accounts.not_found", Language(current_user.language))
            )
        
        # Soft delete
        account.deleted_at = datetime.utcnow()
        
        # Update user's account count
        current_user.decrement_facebook_accounts()
        
        await db.commit()
        
        logger.info(f"Facebook account deleted: {account.email}")
        
        return {"message": _("accounts.deleted_successfully", Language(current_user.language))}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting Facebook account: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )


@accounts_router.post("/{account_id}/test-login")
async def test_facebook_login(
    account_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Test Facebook account login
    اختبار تسجيل دخول حساب فيسبوك
    """
    try:
        result = await db.execute(
            select(FacebookAccount).where(
                and_(
                    FacebookAccount.id == account_id,
                    FacebookAccount.user_id == current_user.id
                )
            )
        )
        account = result.scalar_one_or_none()
        
        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=_("accounts.not_found", Language(current_user.language))
            )
        
        # TODO: Implement actual login test using browser automation
        # For now, just return a mock response
        
        return {
            "success": True,
            "message": _("accounts.login_test_successful", Language(current_user.language)),
            "account_status": account.status
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error testing Facebook login: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )
