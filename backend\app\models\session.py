"""
Browser Session model for managing browser sessions
نموذج جلسة المتصفح لإدارة جلسات المتصفح
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid
import enum
from datetime import datetime, timedelta

from app.core.database import Base


class SessionStatus(str, enum.Enum):
    """Browser session status"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    TERMINATED = "terminated"
    ERROR = "error"


class BrowserType(str, enum.Enum):
    """Browser type"""
    CHROMIUM = "chromium"
    FIREFOX = "firefox"
    WEBKIT = "webkit"


class BrowserSession(Base):
    """Browser Session model"""
    __tablename__ = "browser_sessions"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Foreign key to Facebook account
    facebook_account_id = Column(UUID(as_uuid=True), ForeignKey("facebook_accounts.id"), nullable=False, index=True)
    
    # Session identification
    session_name = Column(String(255), nullable=False)
    browser_type = Column(String(50), default=BrowserType.CHROMIUM.value, nullable=False)
    status = Column(String(50), default=SessionStatus.INACTIVE.value, nullable=False)
    
    # Browser configuration
    user_agent = Column(String(500), nullable=True)
    viewport_width = Column(Integer, default=1920, nullable=False)
    viewport_height = Column(Integer, default=1080, nullable=False)
    headless = Column(Boolean, default=True, nullable=False)
    
    # Profile and data paths
    profile_path = Column(String(500), nullable=True)
    downloads_path = Column(String(500), nullable=True)
    
    # Proxy settings
    proxy_server = Column(String(255), nullable=True)
    proxy_username = Column(String(100), nullable=True)
    proxy_password = Column(String(255), nullable=True)  # Encrypted
    proxy_type = Column(String(20), nullable=True)  # http, https, socks4, socks5
    
    # Browser options
    browser_options = Column(JSON, nullable=True)
    extensions = Column(JSON, nullable=True)  # List of extensions
    
    # Session data
    cookies = Column(JSON, nullable=True)
    local_storage = Column(JSON, nullable=True)
    session_storage = Column(JSON, nullable=True)
    
    # Performance settings
    timeout_seconds = Column(Integer, default=30, nullable=False)
    page_load_timeout = Column(Integer, default=30, nullable=False)
    slow_mo_milliseconds = Column(Integer, default=100, nullable=False)
    
    # Anti-detection settings
    stealth_mode = Column(Boolean, default=True, nullable=False)
    canvas_fingerprint_spoofing = Column(Boolean, default=True, nullable=False)
    webgl_fingerprint_spoofing = Column(Boolean, default=True, nullable=False)
    audio_fingerprint_spoofing = Column(Boolean, default=True, nullable=False)
    
    # Activity tracking
    last_activity_at = Column(DateTime(timezone=True), nullable=True)
    last_page_url = Column(String(500), nullable=True)
    total_page_views = Column(Integer, default=0, nullable=False)
    total_actions = Column(Integer, default=0, nullable=False)
    
    # Session health
    health_score = Column(Integer, default=100, nullable=False)  # 0-100
    error_count = Column(Integer, default=0, nullable=False)
    last_error = Column(Text, nullable=True)
    last_error_at = Column(DateTime(timezone=True), nullable=True)
    
    # Resource usage
    memory_usage_mb = Column(Integer, default=0, nullable=False)
    cpu_usage_percent = Column(Float, default=0.0, nullable=False)
    
    # Session lifecycle
    started_at = Column(DateTime(timezone=True), nullable=True)
    ended_at = Column(DateTime(timezone=True), nullable=True)
    last_heartbeat = Column(DateTime(timezone=True), nullable=True)
    
    # Automation settings
    auto_close_after_minutes = Column(Integer, default=60, nullable=False)
    auto_restart_on_error = Column(Boolean, default=True, nullable=False)
    max_restart_attempts = Column(Integer, default=3, nullable=False)
    restart_attempts = Column(Integer, default=0, nullable=False)
    
    # Logging
    activity_log = Column(JSON, nullable=True)
    debug_mode = Column(Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    facebook_account = relationship("FacebookAccount", back_populates="sessions")
    
    def __repr__(self):
        return f"<BrowserSession(id={self.id}, name={self.session_name}, status={self.status})>"
    
    @property
    def is_active(self) -> bool:
        """Check if session is active"""
        return self.status == SessionStatus.ACTIVE.value
    
    @property
    def is_healthy(self) -> bool:
        """Check if session is healthy"""
        return self.health_score > 50 and self.error_count < 10
    
    @property
    def uptime_minutes(self) -> int:
        """Calculate session uptime in minutes"""
        if not self.started_at:
            return 0
        
        end_time = self.ended_at or datetime.utcnow()
        uptime = end_time - self.started_at
        return int(uptime.total_seconds() / 60)
    
    @property
    def should_auto_close(self) -> bool:
        """Check if session should auto close"""
        if not self.started_at or not self.auto_close_after_minutes:
            return False
        
        auto_close_time = self.started_at + timedelta(minutes=self.auto_close_after_minutes)
        return datetime.utcnow() > auto_close_time
    
    @property
    def is_stale(self) -> bool:
        """Check if session is stale (no activity for 30 minutes)"""
        if not self.last_activity_at:
            return True
        
        stale_time = self.last_activity_at + timedelta(minutes=30)
        return datetime.utcnow() > stale_time
    
    def start_session(self):
        """Start the browser session"""
        self.status = SessionStatus.ACTIVE.value
        self.started_at = datetime.utcnow()
        self.last_activity_at = datetime.utcnow()
        self.last_heartbeat = datetime.utcnow()
        self.restart_attempts = 0
    
    def end_session(self):
        """End the browser session"""
        self.status = SessionStatus.TERMINATED.value
        self.ended_at = datetime.utcnow()
    
    def suspend_session(self):
        """Suspend the browser session"""
        self.status = SessionStatus.SUSPENDED.value
    
    def resume_session(self):
        """Resume the browser session"""
        self.status = SessionStatus.ACTIVE.value
        self.last_activity_at = datetime.utcnow()
    
    def record_error(self, error_message: str):
        """Record an error"""
        self.error_count += 1
        self.last_error = error_message
        self.last_error_at = datetime.utcnow()
        self.health_score = max(0, self.health_score - 10)
        
        if self.error_count >= 10:
            self.status = SessionStatus.ERROR.value
    
    def record_activity(self, action: str, details: dict = None):
        """Record session activity"""
        self.total_actions += 1
        self.last_activity_at = datetime.utcnow()
        self.last_heartbeat = datetime.utcnow()
        
        # Add to activity log
        if not self.activity_log:
            self.activity_log = []
        
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "action": action,
            "details": details or {}
        }
        
        self.activity_log.append(log_entry)
        
        # Keep only last 100 activities
        if len(self.activity_log) > 100:
            self.activity_log = self.activity_log[-100:]
    
    def record_page_view(self, url: str):
        """Record page view"""
        self.total_page_views += 1
        self.last_page_url = url
        self.record_activity("page_view", {"url": url})
    
    def update_heartbeat(self):
        """Update session heartbeat"""
        self.last_heartbeat = datetime.utcnow()
        
        # Improve health score for active sessions
        if self.health_score < 100:
            self.health_score = min(100, self.health_score + 1)
    
    def update_resource_usage(self, memory_mb: int, cpu_percent: float):
        """Update resource usage"""
        self.memory_usage_mb = memory_mb
        self.cpu_usage_percent = cpu_percent
    
    def save_cookies(self, cookies_data: list):
        """Save cookies data"""
        self.cookies = cookies_data
    
    def save_local_storage(self, storage_data: dict):
        """Save local storage data"""
        self.local_storage = storage_data
    
    def save_session_storage(self, storage_data: dict):
        """Save session storage data"""
        self.session_storage = storage_data
    
    def can_restart(self) -> bool:
        """Check if session can be restarted"""
        return (
            self.auto_restart_on_error and
            self.restart_attempts < self.max_restart_attempts
        )
    
    def attempt_restart(self):
        """Attempt to restart session"""
        if self.can_restart():
            self.restart_attempts += 1
            self.status = SessionStatus.INACTIVE.value
            self.error_count = 0
            self.last_error = None
            self.health_score = 80  # Start with reduced health
            return True
        return False
    
    def get_browser_options(self) -> dict:
        """Get browser options for launching"""
        options = {
            "headless": self.headless,
            "viewport": {
                "width": self.viewport_width,
                "height": self.viewport_height
            },
            "user_agent": self.user_agent,
            "timeout": self.timeout_seconds * 1000,  # Convert to milliseconds
            "slow_mo": self.slow_mo_milliseconds,
        }
        
        # Add proxy settings
        if self.proxy_server:
            options["proxy"] = {
                "server": self.proxy_server,
                "username": self.proxy_username,
                "password": self.proxy_password
            }
        
        # Add custom options
        if self.browser_options:
            options.update(self.browser_options)
        
        return options
    
    def cleanup_old_data(self):
        """Cleanup old session data"""
        # Clear old activity logs (keep only last 7 days)
        if self.activity_log:
            cutoff_date = datetime.utcnow() - timedelta(days=7)
            self.activity_log = [
                log for log in self.activity_log
                if datetime.fromisoformat(log["timestamp"]) > cutoff_date
            ]
