"""
Post model for tracking Facebook Marketplace posts
نموذج المنشور لتتبع منشورات فيسبوك ماركت بليس
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, JSON, Float
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid
import enum
from datetime import datetime

from app.core.database import Base


class PostStatus(str, enum.Enum):
    """Post status"""
    PENDING = "pending"
    SCHEDULED = "scheduled"
    POSTING = "posting"
    POSTED = "posted"
    FAILED = "failed"
    REMOVED = "removed"
    EXPIRED = "expired"
    SOLD = "sold"


class PostType(str, enum.Enum):
    """Post type"""
    MARKETPLACE = "marketplace"
    GROUP = "group"
    PAGE = "page"
    STORY = "story"


class Post(Base):
    """Post model"""
    __tablename__ = "posts"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Foreign keys
    facebook_account_id = Column(UUID(as_uuid=True), ForeignKey("facebook_accounts.id"), nullable=False, index=True)
    template_id = Column(UUID(as_uuid=True), ForeignKey("templates.id"), nullable=True, index=True)
    campaign_id = Column(UUID(as_uuid=True), ForeignKey("campaigns.id"), nullable=True, index=True)
    
    # Post identification
    facebook_post_id = Column(String(100), nullable=True, index=True)
    post_url = Column(String(500), nullable=True)
    post_type = Column(String(50), default=PostType.MARKETPLACE.value, nullable=False)
    status = Column(String(50), default=PostStatus.PENDING.value, nullable=False)
    
    # Content
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    price = Column(Float, nullable=True)
    currency = Column(String(3), default="USD", nullable=False)
    
    # Product details
    category = Column(String(100), nullable=False)
    subcategory = Column(String(100), nullable=True)
    condition = Column(String(50), nullable=True)
    brand = Column(String(100), nullable=True)
    
    # Location
    location = Column(String(255), nullable=True)
    city = Column(String(100), nullable=True)
    state = Column(String(100), nullable=True)
    country = Column(String(100), nullable=True)
    
    # Images
    image_urls = Column(ARRAY(String), nullable=True)
    uploaded_image_ids = Column(ARRAY(String), nullable=True)  # Facebook image IDs
    
    # Scheduling
    scheduled_at = Column(DateTime(timezone=True), nullable=True)
    posted_at = Column(DateTime(timezone=True), nullable=True)
    expires_at = Column(DateTime(timezone=True), nullable=True)
    
    # Performance metrics
    views_count = Column(Integer, default=0, nullable=False)
    saves_count = Column(Integer, default=0, nullable=False)
    shares_count = Column(Integer, default=0, nullable=False)
    messages_count = Column(Integer, default=0, nullable=False)
    clicks_count = Column(Integer, default=0, nullable=False)
    
    # Engagement tracking
    last_view_at = Column(DateTime(timezone=True), nullable=True)
    last_message_at = Column(DateTime(timezone=True), nullable=True)
    last_update_at = Column(DateTime(timezone=True), nullable=True)
    
    # Error handling
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0, nullable=False)
    max_retries = Column(Integer, default=3, nullable=False)
    last_retry_at = Column(DateTime(timezone=True), nullable=True)
    
    # Anti-ban tracking
    posting_delay_seconds = Column(Integer, nullable=True)
    user_agent_used = Column(String(500), nullable=True)
    proxy_used = Column(String(255), nullable=True)
    
    # Content variations used
    title_variation_used = Column(String(255), nullable=True)
    description_variation_used = Column(Text, nullable=True)
    
    # Marketplace specific
    marketplace_category_id = Column(String(50), nullable=True)
    marketplace_listing_id = Column(String(100), nullable=True)
    is_boosted = Column(Boolean, default=False, nullable=False)
    boost_amount = Column(Float, nullable=True)
    
    # Status tracking
    is_active = Column(Boolean, default=True, nullable=False)
    is_sold = Column(Boolean, default=False, nullable=False)
    is_hidden = Column(Boolean, default=False, nullable=False)
    is_flagged = Column(Boolean, default=False, nullable=False)
    
    # Automation metadata
    automation_data = Column(JSON, nullable=True)
    posting_session_id = Column(String(100), nullable=True)
    
    # Tags and keywords
    tags = Column(ARRAY(String), nullable=True)
    keywords = Column(ARRAY(String), nullable=True)
    
    # Contact information
    contact_method = Column(String(50), nullable=True)
    phone_number = Column(String(20), nullable=True)
    email = Column(String(255), nullable=True)
    
    # Notes
    notes = Column(Text, nullable=True)
    admin_notes = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    facebook_account = relationship("FacebookAccount", back_populates="posts")
    template = relationship("Template", back_populates="posts")
    campaign = relationship("Campaign", back_populates="posts")
    
    def __repr__(self):
        return f"<Post(id={self.id}, title={self.title[:50]}, status={self.status})>"
    
    @property
    def is_posted(self) -> bool:
        """Check if post is successfully posted"""
        return self.status == PostStatus.POSTED.value
    
    @property
    def is_failed(self) -> bool:
        """Check if post failed"""
        return self.status == PostStatus.FAILED.value
    
    @property
    def is_pending(self) -> bool:
        """Check if post is pending"""
        return self.status == PostStatus.PENDING.value
    
    @property
    def is_scheduled(self) -> bool:
        """Check if post is scheduled"""
        return self.status == PostStatus.SCHEDULED.value
    
    @property
    def can_retry(self) -> bool:
        """Check if post can be retried"""
        return self.retry_count < self.max_retries and self.is_failed
    
    @property
    def engagement_rate(self) -> float:
        """Calculate engagement rate"""
        if self.views_count == 0:
            return 0.0
        
        total_engagement = self.saves_count + self.shares_count + self.messages_count
        return (total_engagement / self.views_count) * 100
    
    @property
    def formatted_price(self) -> str:
        """Get formatted price with currency"""
        if self.price is None:
            return "N/A"
        return f"{self.currency} {self.price:,.2f}"
    
    @property
    def time_since_posted(self) -> str:
        """Get time since posted in human readable format"""
        if not self.posted_at:
            return "Not posted"
        
        delta = datetime.utcnow() - self.posted_at
        
        if delta.days > 0:
            return f"{delta.days} days ago"
        elif delta.seconds > 3600:
            hours = delta.seconds // 3600
            return f"{hours} hours ago"
        elif delta.seconds > 60:
            minutes = delta.seconds // 60
            return f"{minutes} minutes ago"
        else:
            return "Just now"
    
    def mark_as_posted(self, facebook_post_id: str = None, post_url: str = None):
        """Mark post as successfully posted"""
        self.status = PostStatus.POSTED.value
        self.posted_at = datetime.utcnow()
        if facebook_post_id:
            self.facebook_post_id = facebook_post_id
        if post_url:
            self.post_url = post_url
    
    def mark_as_failed(self, error_message: str):
        """Mark post as failed"""
        self.status = PostStatus.FAILED.value
        self.error_message = error_message
        self.last_retry_at = datetime.utcnow()
    
    def mark_as_sold(self):
        """Mark post as sold"""
        self.status = PostStatus.SOLD.value
        self.is_sold = True
        self.is_active = False
    
    def mark_as_removed(self):
        """Mark post as removed"""
        self.status = PostStatus.REMOVED.value
        self.is_active = False
    
    def mark_as_expired(self):
        """Mark post as expired"""
        self.status = PostStatus.EXPIRED.value
        self.is_active = False
    
    def increment_retry(self):
        """Increment retry count"""
        self.retry_count += 1
        self.last_retry_at = datetime.utcnow()
        if self.retry_count < self.max_retries:
            self.status = PostStatus.PENDING.value
            self.error_message = None
    
    def update_metrics(self, views: int = None, saves: int = None, shares: int = None, 
                      messages: int = None, clicks: int = None):
        """Update performance metrics"""
        if views is not None:
            self.views_count = views
            self.last_view_at = datetime.utcnow()
        if saves is not None:
            self.saves_count = saves
        if shares is not None:
            self.shares_count = shares
        if messages is not None:
            self.messages_count = messages
            if messages > 0:
                self.last_message_at = datetime.utcnow()
        if clicks is not None:
            self.clicks_count = clicks
        
        self.last_update_at = datetime.utcnow()
    
    def add_tag(self, tag: str):
        """Add tag to post"""
        if not self.tags:
            self.tags = []
        if tag not in self.tags:
            self.tags.append(tag)
    
    def remove_tag(self, tag: str):
        """Remove tag from post"""
        if self.tags and tag in self.tags:
            self.tags.remove(tag)
    
    def add_keyword(self, keyword: str):
        """Add keyword to post"""
        if not self.keywords:
            self.keywords = []
        if keyword not in self.keywords:
            self.keywords.append(keyword)
    
    def set_automation_data(self, data: dict):
        """Set automation metadata"""
        self.automation_data = data
    
    def get_automation_data(self, key: str, default=None):
        """Get automation metadata"""
        if not self.automation_data:
            return default
        return self.automation_data.get(key, default)
    
    def is_ready_for_posting(self) -> bool:
        """Check if post is ready for posting"""
        return (
            self.status in [PostStatus.PENDING.value, PostStatus.SCHEDULED.value] and
            self.title and
            self.description and
            self.category
        )
    
    def should_be_posted_now(self) -> bool:
        """Check if scheduled post should be posted now"""
        if not self.is_scheduled:
            return False
        
        if not self.scheduled_at:
            return True
        
        return datetime.utcnow() >= self.scheduled_at
