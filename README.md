# 🚀 Facebook Marketplace Desktop Application

## منصة إدارة فيسبوك ماركت بليس الاحترافية

A professional, full-featured Facebook Marketplace automation platform with advanced SaaS capabilities, multi-language support (Arabic/English), and comprehensive automation features.

منصة احترافية ومتكاملة لأتمتة فيسبوك ماركت بليس مع إمكانيات SaaS متقدمة ودعم متعدد اللغات (العربية/الإنجليزية) وميزات أتمتة شاملة.

## ✨ Key Features / الميزات الرئيسية

### 🔐 Advanced Authentication System / نظام المصادقة المتقدم
- **Multi-method Facebook login** / تسجيل دخول فيسبوك متعدد الطرق
  - Email & Password / البريد الإلكتروني وكلمة المرور
  - Access Token / رمز الوصول
  - Browser Profile Import / استيراد ملف المتصفح
  - Cookies Integration / تكامل ملفات تعريف الارتباط
- **JWT Authentication** with refresh tokens / مصادقة JWT مع رموز التحديث
- **Two-Factor Authentication (2FA)** / المصادقة الثنائية
- **Role-based Access Control** / التحكم في الوصول حسب الأدوار

### 🤖 Intelligent Browser Automation / أتمتة المتصفح الذكية
- **Independent Browser Controller** / وحدة تحكم مستقلة بالمتصفح
- **Anti-Ban Engine** with human-like behavior / محرك مقاومة الحظر مع سلوك بشري
- **Session Management** with persistence / إدارة الجلسات مع الاستمرارية
- **Proxy Support** with rotation / دعم البروكسي مع التدوير
- **Stealth Mode** with fingerprint spoofing / الوضع الخفي مع تمويه البصمة

### 📝 Advanced Template System / نظام القوالب المتقدم
- **Product Templates** with variations / قوالب المنتجات مع التنويعات
- **AI Content Generation** / توليد المحتوى بالذكاء الاصطناعي
- **Content Spinning** for anti-detection / تدوير المحتوى لمقاومة الكشف
- **Category Management** / إدارة الفئات
- **Image Management** / إدارة الصور

### 🎯 Campaign Management / إدارة الحملات
- **Automated Posting Campaigns** / حملات النشر الآلي
- **Scheduling System** / نظام الجدولة
- **A/B Testing** / اختبار A/B
- **Performance Tracking** / تتبع الأداء
- **Smart Retry Logic** / منطق إعادة المحاولة الذكي

### 📊 Comprehensive Analytics / تحليلات شاملة
- **Real-time Dashboard** / لوحة تحكم فورية
- **Performance Metrics** / مقاييس الأداء
- **Success Rate Tracking** / تتبع معدل النجاح
- **Account Health Monitoring** / مراقبة صحة الحسابات
- **Custom Reports** / تقارير مخصصة

### 🌍 Multi-Language Support / دعم متعدد اللغات
- **Arabic (RTL)** with Cairo font / العربية (من اليمين لليسار) مع خط القاهرة
- **English (LTR)** with Inter font / الإنجليزية (من اليسار لليمين) مع خط Inter
- **Dynamic Direction Switching** / تبديل الاتجاه الديناميكي
- **Localized Content** / محتوى محلي

### 🎨 Modern UI/UX / واجهة مستخدم حديثة
- **Responsive Design** / تصميم متجاوب
- **Dark/Light Mode** / الوضع المظلم/المضيء
- **Tailwind CSS** styling / تصميم Tailwind CSS
- **Smooth Animations** / رسوم متحركة سلسة
- **Accessibility Features** / ميزات إمكانية الوصول

### 💼 SaaS Features / ميزات SaaS
- **Subscription Plans** / خطط الاشتراك
  - Starter (5 accounts, 100 posts/month) / المبتدئ
  - Professional (25 accounts, 1000 posts/month) / المحترف
  - Enterprise (Unlimited) / المؤسسي
- **Multi-tenant Architecture** / معمارية متعددة المستأجرين
- **API Access** / وصول API
- **White-label Solutions** / حلول العلامة البيضاء

## 🏗️ Technology Stack / المكدس التقني

### Backend / الخلفية
- **FastAPI** - Modern Python web framework / إطار عمل Python حديث
- **SQLAlchemy** - ORM with async support / ORM مع دعم غير متزامن
- **PostgreSQL** - Production database / قاعدة بيانات الإنتاج
- **Redis** - Caching and queue / التخزين المؤقت والطوابير
- **Celery** - Background tasks / المهام الخلفية
- **Playwright** - Browser automation / أتمتة المتصفح
- **JWT** - Authentication / المصادقة

### Frontend / الواجهة الأمامية
- **React 18** - UI library / مكتبة واجهة المستخدم
- **TypeScript** - Type safety / أمان الأنواع
- **Vite** - Build tool / أداة البناء
- **Tailwind CSS** - Styling / التصميم
- **Zustand** - State management / إدارة الحالة
- **React Query** - Data fetching / جلب البيانات
- **React Router** - Navigation / التنقل
- **i18next** - Internationalization / التدويل

### DevOps / عمليات التطوير
- **Docker** - Containerization / الحاويات
- **Docker Compose** - Multi-container apps / تطبيقات متعددة الحاويات
- **PostgreSQL** - Database / قاعدة البيانات
- **Redis** - Cache & Queue / التخزين المؤقت والطوابير

## 🚀 Quick Start / البدء السريع

### Prerequisites / المتطلبات المسبقة
- Docker and Docker Compose
- Git

### Installation / التثبيت

1. **Clone the repository / استنساخ المستودع**
```bash
git clone https://github.com/your-username/facebook-marketplace-platform.git
cd facebook-marketplace-platform
```

2. **Start the application / بدء التطبيق**
```bash
docker-compose up -d
```

3. **Access the application / الوصول للتطبيق**
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

### Default Credentials / بيانات الدخول الافتراضية
- Email: <EMAIL>
- Password: admin123

## 📁 Project Structure / هيكل المشروع

```
facebook-marketplace-platform/
├── backend/                    # FastAPI backend
│   ├── app/
│   │   ├── core/              # Core configurations
│   │   ├── models/            # Database models
│   │   ├── api/               # API endpoints
│   │   ├── automation/        # Browser automation
│   │   ├── services/          # Business logic
│   │   └── tasks/             # Background tasks
│   ├── requirements.txt       # Python dependencies
│   └── Dockerfile            # Backend container
├── frontend/                  # React frontend
│   ├── src/
│   │   ├── components/        # React components
│   │   ├── pages/            # Page components
│   │   ├── store/            # State management
│   │   ├── services/         # API services
│   │   ├── hooks/            # Custom hooks
│   │   ├── utils/            # Utilities
│   │   └── i18n/             # Internationalization
│   ├── package.json          # Node dependencies
│   └── Dockerfile           # Frontend container
├── docker-compose.yml        # Multi-container setup
└── README.md                # This file
```

## 🔧 Configuration / التكوين

### Environment Variables / متغيرات البيئة

Create `.env` file in the backend directory:

```env
# Database
DATABASE_URL=***********************************************/facebook_marketplace

# Redis
REDIS_URL=redis://redis:6379

# Security
SECRET_KEY=your-super-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Facebook
FB_LOGIN_URL=https://www.facebook.com/login
FB_MARKETPLACE_URL=https://www.facebook.com/marketplace

# AI (Optional)
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
```

## 🔒 Security Features / ميزات الأمان

- **Encrypted Password Storage** / تشفير كلمات المرور المخزنة
- **Sensitive Data Encryption** / تشفير البيانات الحساسة
- **Rate Limiting** / تحديد معدل الطلبات
- **CORS Protection** / حماية CORS
- **SQL Injection Prevention** / منع حقن SQL
- **XSS Protection** / حماية XSS
- **CSRF Protection** / حماية CSRF

## 📈 Performance Features / ميزات الأداء

- **Database Connection Pooling** / تجميع اتصالات قاعدة البيانات
- **Redis Caching** / التخزين المؤقت Redis
- **Async/Await Operations** / عمليات غير متزامنة
- **Lazy Loading** / التحميل الكسول
- **Image Optimization** / تحسين الصور
- **Code Splitting** / تقسيم الكود

## 🧪 Testing / الاختبار

```bash
# Backend tests
cd backend
pytest

# Frontend tests
cd frontend
npm test
```

## 📚 API Documentation / توثيق API

The API documentation is automatically generated and available at:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 🤝 Contributing / المساهمة

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License / الترخيص

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support / الدعم

For support, email <EMAIL> or join our Discord server.

## 🙏 Acknowledgments / الشكر والتقدير

- Facebook for the Marketplace platform
- The open-source community
- All contributors and users

---

**Made with ❤️ for the Arabic and English speaking communities**

**صُنع بـ ❤️ للمجتمعات الناطقة بالعربية والإنجليزية**
