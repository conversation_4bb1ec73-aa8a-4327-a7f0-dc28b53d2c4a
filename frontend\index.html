<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Facebook Marketplace Desktop Application</title>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Meta tags for SEO -->
    <meta name="description" content="Professional Facebook Marketplace automation platform with advanced features and multi-language support">
    <meta name="keywords" content="Facebook Marketplace, automation, posting, social media, marketing">
    <meta name="author" content="Facebook Marketplace Platform">
    
    <!-- Open Graph tags -->
    <meta property="og:title" content="Facebook Marketplace Desktop Application">
    <meta property="og:description" content="Professional Facebook Marketplace automation platform">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://your-domain.com">
    
    <!-- Twitter Card tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Facebook Marketplace Desktop Application">
    <meta name="twitter:description" content="Professional Facebook Marketplace automation platform">
    
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
