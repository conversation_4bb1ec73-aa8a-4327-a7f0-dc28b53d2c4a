"""
User model for authentication and user management
نموذج المستخدم للمصادقة وإدارة المستخدمين
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid
import enum
from datetime import datetime

from app.core.database import Base


class UserRole(str, enum.Enum):
    """User roles for role-based access control"""
    ADMIN = "admin"
    MANAGER = "manager"
    EDITOR = "editor"
    VIEWER = "viewer"


class SubscriptionPlan(str, enum.Enum):
    """Subscription plans"""
    STARTER = "starter"
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"
    CUSTOM = "custom"


class UserStatus(str, enum.Enum):
    """User account status"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING_VERIFICATION = "pending_verification"


class User(Base):
    """User model"""
    __tablename__ = "users"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Basic information
    email = Column(String(255), unique=True, index=True, nullable=False)
    username = Column(String(100), unique=True, index=True, nullable=True)
    full_name = Column(String(255), nullable=True)
    hashed_password = Column(String(255), nullable=False)
    
    # Profile information
    phone_number = Column(String(20), nullable=True)
    avatar_url = Column(String(500), nullable=True)
    bio = Column(Text, nullable=True)
    website = Column(String(255), nullable=True)
    location = Column(String(255), nullable=True)
    
    # Account settings
    role = Column(SQLEnum(UserRole), default=UserRole.VIEWER, nullable=False)
    status = Column(SQLEnum(UserStatus), default=UserStatus.PENDING_VERIFICATION, nullable=False)
    subscription_plan = Column(SQLEnum(SubscriptionPlan), default=SubscriptionPlan.STARTER, nullable=False)
    
    # Security settings
    is_email_verified = Column(Boolean, default=False, nullable=False)
    is_2fa_enabled = Column(Boolean, default=False, nullable=False)
    two_fa_secret = Column(String(255), nullable=True)
    
    # API access
    api_key_hash = Column(String(255), nullable=True)
    api_key_created_at = Column(DateTime(timezone=True), nullable=True)
    api_key_last_used = Column(DateTime(timezone=True), nullable=True)
    
    # Preferences
    language = Column(String(5), default="en", nullable=False)
    timezone = Column(String(50), default="UTC", nullable=False)
    theme = Column(String(20), default="light", nullable=False)
    
    # Subscription details
    subscription_start_date = Column(DateTime(timezone=True), nullable=True)
    subscription_end_date = Column(DateTime(timezone=True), nullable=True)
    subscription_auto_renew = Column(Boolean, default=True, nullable=False)
    
    # Usage limits
    max_facebook_accounts = Column(Integer, default=5, nullable=False)
    max_monthly_posts = Column(Integer, default=100, nullable=False)
    current_facebook_accounts = Column(Integer, default=0, nullable=False)
    current_monthly_posts = Column(Integer, default=0, nullable=False)
    
    # Login tracking
    last_login_at = Column(DateTime(timezone=True), nullable=True)
    last_login_ip = Column(String(45), nullable=True)
    login_count = Column(Integer, default=0, nullable=False)
    failed_login_attempts = Column(Integer, default=0, nullable=False)
    last_failed_login = Column(DateTime(timezone=True), nullable=True)
    
    # Password reset
    password_reset_token = Column(String(255), nullable=True)
    password_reset_expires = Column(DateTime(timezone=True), nullable=True)
    
    # Email verification
    email_verification_token = Column(String(255), nullable=True)
    email_verification_expires = Column(DateTime(timezone=True), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    facebook_accounts = relationship("FacebookAccount", back_populates="user", cascade="all, delete-orphan")
    templates = relationship("Template", back_populates="user", cascade="all, delete-orphan")
    campaigns = relationship("Campaign", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(id={self.id}, email={self.email}, role={self.role})>"
    
    @property
    def is_active(self) -> bool:
        """Check if user is active"""
        return self.status == UserStatus.ACTIVE
    
    @property
    def is_admin(self) -> bool:
        """Check if user is admin"""
        return self.role == UserRole.ADMIN
    
    @property
    def is_manager(self) -> bool:
        """Check if user is manager or admin"""
        return self.role in [UserRole.ADMIN, UserRole.MANAGER]
    
    @property
    def can_edit(self) -> bool:
        """Check if user can edit content"""
        return self.role in [UserRole.ADMIN, UserRole.MANAGER, UserRole.EDITOR]
    
    @property
    def subscription_active(self) -> bool:
        """Check if subscription is active"""
        if not self.subscription_end_date:
            return False
        return datetime.utcnow() < self.subscription_end_date
    
    @property
    def can_add_facebook_account(self) -> bool:
        """Check if user can add more Facebook accounts"""
        if self.max_facebook_accounts == -1:  # Unlimited
            return True
        return self.current_facebook_accounts < self.max_facebook_accounts
    
    @property
    def can_create_post(self) -> bool:
        """Check if user can create more posts this month"""
        if self.max_monthly_posts == -1:  # Unlimited
            return True
        return self.current_monthly_posts < self.max_monthly_posts
    
    def increment_facebook_accounts(self):
        """Increment Facebook accounts count"""
        self.current_facebook_accounts += 1
    
    def decrement_facebook_accounts(self):
        """Decrement Facebook accounts count"""
        if self.current_facebook_accounts > 0:
            self.current_facebook_accounts -= 1
    
    def increment_monthly_posts(self):
        """Increment monthly posts count"""
        self.current_monthly_posts += 1
    
    def reset_monthly_posts(self):
        """Reset monthly posts count (called monthly)"""
        self.current_monthly_posts = 0
    
    def update_last_login(self, ip_address: str = None):
        """Update last login information"""
        self.last_login_at = datetime.utcnow()
        self.last_login_ip = ip_address
        self.login_count += 1
        self.failed_login_attempts = 0  # Reset failed attempts on successful login
    
    def record_failed_login(self):
        """Record failed login attempt"""
        self.failed_login_attempts += 1
        self.last_failed_login = datetime.utcnow()
    
    def is_locked(self) -> bool:
        """Check if account is locked due to failed login attempts"""
        return self.failed_login_attempts >= 5
    
    def unlock_account(self):
        """Unlock account by resetting failed login attempts"""
        self.failed_login_attempts = 0
        self.last_failed_login = None
