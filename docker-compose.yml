version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: fb_marketplace_db
    environment:
      POSTGRES_DB: facebook_marketplace
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - fb_marketplace_network

  # Redis Cache & Queue
  redis:
    image: redis:7-alpine
    container_name: fb_marketplace_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - fb_marketplace_network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: fb_marketplace_backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=***********************************************/facebook_marketplace
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=your-super-secret-key-change-in-production
      - ENVIRONMENT=development
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
      - browser_profiles:/app/browser_profiles
    networks:
      - fb_marketplace_network

  # Celery Worker
  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: fb_marketplace_celery
    command: celery -A app.tasks.celery_tasks worker --loglevel=info
    environment:
      - DATABASE_URL=***********************************************/facebook_marketplace
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=your-super-secret-key-change-in-production
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
      - browser_profiles:/app/browser_profiles
    networks:
      - fb_marketplace_network

  # Celery Beat (Scheduler)
  celery_beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: fb_marketplace_beat
    command: celery -A app.tasks.celery_tasks beat --loglevel=info
    environment:
      - DATABASE_URL=***********************************************/facebook_marketplace
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=your-super-secret-key-change-in-production
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
    networks:
      - fb_marketplace_network

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: fb_marketplace_frontend
    ports:
      - "3000:3000"
    environment:
      - VITE_API_URL=http://localhost:8000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - fb_marketplace_network

volumes:
  postgres_data:
  redis_data:
  browser_profiles:

networks:
  fb_marketplace_network:
    driver: bridge
