"""
Template model for product templates
نموذج القوالب لقوالب المنتجات
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, JSON, Float
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid
import enum
from datetime import datetime

from app.core.database import Base


class TemplateStatus(str, enum.Enum):
    """Template status"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DRAFT = "draft"
    ARCHIVED = "archived"


class ProductCategory(str, enum.Enum):
    """Facebook Marketplace categories"""
    VEHICLES = "vehicles"
    PROPERTY_RENTALS = "property_rentals"
    APPAREL = "apparel"
    CLASSIFIEDS = "classifieds"
    ELECTRONICS = "electronics"
    ENTERTAINMENT = "entertainment"
    FAMILY = "family"
    FREE_STUFF = "free_stuff"
    GARDEN_OUTDOOR = "garden_outdoor"
    HOBBIES = "hobbies"
    HOME_GOODS = "home_goods"
    HOME_IMPROVEMENT_SUPPLIES = "home_improvement_supplies"
    HOME_SALES = "home_sales"
    MUSICAL_INSTRUMENTS = "musical_instruments"
    OFFICE_SUPPLIES = "office_supplies"
    PET_SUPPLIES = "pet_supplies"
    SPORTING_GOODS = "sporting_goods"
    TOYS_GAMES = "toys_games"
    VEHICLES_PARTS = "vehicles_parts"


class Template(Base):
    """Product Template model"""
    __tablename__ = "templates"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Foreign key to user
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    
    # Template basic info
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    status = Column(String(50), default=TemplateStatus.ACTIVE.value, nullable=False)
    
    # Product information
    title = Column(String(255), nullable=False)
    product_description = Column(Text, nullable=False)
    category = Column(String(100), nullable=False)
    subcategory = Column(String(100), nullable=True)
    
    # Pricing
    price = Column(Float, nullable=False)
    currency = Column(String(3), default="USD", nullable=False)
    is_negotiable = Column(Boolean, default=True, nullable=False)
    
    # Product details
    condition = Column(String(50), nullable=True)  # new, used, refurbished
    brand = Column(String(100), nullable=True)
    model = Column(String(100), nullable=True)
    color = Column(String(50), nullable=True)
    size = Column(String(50), nullable=True)
    
    # Location
    location = Column(String(255), nullable=True)
    city = Column(String(100), nullable=True)
    state = Column(String(100), nullable=True)
    country = Column(String(100), nullable=True)
    zip_code = Column(String(20), nullable=True)
    
    # Images
    image_urls = Column(ARRAY(String), nullable=True)
    main_image_url = Column(String(500), nullable=True)
    
    # Content variations for anti-ban
    title_variations = Column(ARRAY(String), nullable=True)
    description_variations = Column(ARRAY(Text), nullable=True)
    
    # SEO and keywords
    keywords = Column(ARRAY(String), nullable=True)
    tags = Column(ARRAY(String), nullable=True)
    
    # Posting settings
    auto_repost = Column(Boolean, default=False, nullable=False)
    repost_interval_hours = Column(Integer, default=24, nullable=False)
    max_reposts = Column(Integer, default=5, nullable=False)
    
    # Availability
    is_available = Column(Boolean, default=True, nullable=False)
    quantity = Column(Integer, default=1, nullable=False)
    
    # Contact information
    contact_method = Column(String(50), default="messenger", nullable=False)  # messenger, phone, email
    phone_number = Column(String(20), nullable=True)
    email = Column(String(255), nullable=True)
    
    # Advanced settings
    custom_fields = Column(JSON, nullable=True)
    posting_schedule = Column(JSON, nullable=True)  # Schedule for posting
    target_groups = Column(ARRAY(String), nullable=True)  # Facebook groups to post to
    
    # AI content generation settings
    use_ai_content = Column(Boolean, default=False, nullable=False)
    ai_prompt = Column(Text, nullable=True)
    ai_model = Column(String(50), nullable=True)
    
    # Usage statistics
    times_used = Column(Integer, default=0, nullable=False)
    successful_posts = Column(Integer, default=0, nullable=False)
    failed_posts = Column(Integer, default=0, nullable=False)
    last_used_at = Column(DateTime(timezone=True), nullable=True)
    
    # Template sharing
    is_public = Column(Boolean, default=False, nullable=False)
    is_featured = Column(Boolean, default=False, nullable=False)
    downloads_count = Column(Integer, default=0, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="templates")
    campaigns = relationship("Campaign", back_populates="template")
    posts = relationship("Post", back_populates="template")
    
    def __repr__(self):
        return f"<Template(id={self.id}, name={self.name}, category={self.category})>"
    
    @property
    def is_active(self) -> bool:
        """Check if template is active"""
        return self.status == TemplateStatus.ACTIVE.value
    
    @property
    def success_rate(self) -> float:
        """Calculate template success rate"""
        total_posts = self.successful_posts + self.failed_posts
        if total_posts == 0:
            return 0.0
        return (self.successful_posts / total_posts) * 100
    
    @property
    def formatted_price(self) -> str:
        """Get formatted price with currency"""
        return f"{self.currency} {self.price:,.2f}"
    
    def increment_usage(self):
        """Increment usage counter"""
        self.times_used += 1
        self.last_used_at = datetime.utcnow()
    
    def record_successful_post(self):
        """Record successful post"""
        self.successful_posts += 1
        self.increment_usage()
    
    def record_failed_post(self):
        """Record failed post"""
        self.failed_posts += 1
        self.increment_usage()
    
    def get_random_title(self) -> str:
        """Get random title variation"""
        import random
        if self.title_variations and len(self.title_variations) > 0:
            return random.choice(self.title_variations)
        return self.title
    
    def get_random_description(self) -> str:
        """Get random description variation"""
        import random
        if self.description_variations and len(self.description_variations) > 0:
            return random.choice(self.description_variations)
        return self.product_description
    
    def add_title_variation(self, variation: str):
        """Add title variation"""
        if not self.title_variations:
            self.title_variations = []
        if variation not in self.title_variations:
            self.title_variations.append(variation)
    
    def add_description_variation(self, variation: str):
        """Add description variation"""
        if not self.description_variations:
            self.description_variations = []
        if variation not in self.description_variations:
            self.description_variations.append(variation)
    
    def add_keyword(self, keyword: str):
        """Add keyword"""
        if not self.keywords:
            self.keywords = []
        if keyword not in self.keywords:
            self.keywords.append(keyword)
    
    def add_tag(self, tag: str):
        """Add tag"""
        if not self.tags:
            self.tags = []
        if tag not in self.tags:
            self.tags.append(tag)
    
    def add_image_url(self, url: str):
        """Add image URL"""
        if not self.image_urls:
            self.image_urls = []
        if url not in self.image_urls:
            self.image_urls.append(url)
            if not self.main_image_url:
                self.main_image_url = url
    
    def set_main_image(self, url: str):
        """Set main image URL"""
        self.main_image_url = url
        if not self.image_urls:
            self.image_urls = []
        if url not in self.image_urls:
            self.image_urls.insert(0, url)
    
    def clone(self, new_name: str = None) -> "Template":
        """Clone template with new name"""
        clone_data = {
            "name": new_name or f"{self.name} (Copy)",
            "description": self.description,
            "title": self.title,
            "product_description": self.product_description,
            "category": self.category,
            "subcategory": self.subcategory,
            "price": self.price,
            "currency": self.currency,
            "is_negotiable": self.is_negotiable,
            "condition": self.condition,
            "brand": self.brand,
            "model": self.model,
            "color": self.color,
            "size": self.size,
            "location": self.location,
            "city": self.city,
            "state": self.state,
            "country": self.country,
            "zip_code": self.zip_code,
            "image_urls": self.image_urls.copy() if self.image_urls else None,
            "main_image_url": self.main_image_url,
            "title_variations": self.title_variations.copy() if self.title_variations else None,
            "description_variations": self.description_variations.copy() if self.description_variations else None,
            "keywords": self.keywords.copy() if self.keywords else None,
            "tags": self.tags.copy() if self.tags else None,
            "contact_method": self.contact_method,
            "phone_number": self.phone_number,
            "email": self.email,
            "custom_fields": self.custom_fields.copy() if self.custom_fields else None,
        }
        
        return Template(**clone_data)
