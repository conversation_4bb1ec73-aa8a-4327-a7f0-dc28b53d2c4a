"""
Templates API endpoints
نقاط نهاية API للقوالب
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
import logging

from app.core.database import get_db
from app.core.i18n import _, Language
from app.models.user import User
from app.models.template import Template, TemplateStatus, ProductCategory
from app.api.auth import get_current_active_user

logger = logging.getLogger(__name__)

# Create router
templates_router = APIRouter()


# Pydantic models
class TemplateCreate(BaseModel):
    name: str
    description: Optional[str] = None
    title: str
    product_description: str
    category: ProductCategory
    subcategory: Optional[str] = None
    price: float
    currency: str = "USD"
    is_negotiable: bool = True
    condition: Optional[str] = None
    brand: Optional[str] = None
    model: Optional[str] = None
    color: Optional[str] = None
    size: Optional[str] = None
    location: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    country: Optional[str] = None
    zip_code: Optional[str] = None
    image_urls: Optional[List[str]] = None
    keywords: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    contact_method: str = "messenger"
    phone_number: Optional[str] = None
    email: Optional[str] = None


class TemplateUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    title: Optional[str] = None
    product_description: Optional[str] = None
    category: Optional[ProductCategory] = None
    subcategory: Optional[str] = None
    price: Optional[float] = None
    currency: Optional[str] = None
    is_negotiable: Optional[bool] = None
    condition: Optional[str] = None
    brand: Optional[str] = None
    model: Optional[str] = None
    color: Optional[str] = None
    size: Optional[str] = None
    location: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    country: Optional[str] = None
    zip_code: Optional[str] = None
    image_urls: Optional[List[str]] = None
    keywords: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    contact_method: Optional[str] = None
    phone_number: Optional[str] = None
    email: Optional[str] = None
    status: Optional[TemplateStatus] = None


class TemplateResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    title: str
    product_description: str
    category: str
    subcategory: Optional[str]
    price: float
    currency: str
    formatted_price: str
    is_negotiable: bool
    condition: Optional[str]
    brand: Optional[str]
    model: Optional[str]
    color: Optional[str]
    size: Optional[str]
    location: Optional[str]
    city: Optional[str]
    state: Optional[str]
    country: Optional[str]
    zip_code: Optional[str]
    image_urls: Optional[List[str]]
    main_image_url: Optional[str]
    keywords: Optional[List[str]]
    tags: Optional[List[str]]
    contact_method: str
    phone_number: Optional[str]
    email: Optional[str]
    status: str
    times_used: int
    successful_posts: int
    failed_posts: int
    success_rate: float
    last_used_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class TemplateList(BaseModel):
    templates: List[TemplateResponse]
    total: int
    page: int
    per_page: int


@templates_router.get("/", response_model=TemplateList)
async def get_templates(
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    status: Optional[TemplateStatus] = None,
    category: Optional[ProductCategory] = None,
    search: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get user's templates
    الحصول على قوالب المستخدم
    """
    try:
        # Build query
        query = select(Template).where(Template.user_id == current_user.id)
        
        # Add filters
        if status:
            query = query.where(Template.status == status.value)
        
        if category:
            query = query.where(Template.category == category.value)
        
        if search:
            query = query.where(
                Template.name.ilike(f"%{search}%") |
                Template.title.ilike(f"%{search}%") |
                Template.product_description.ilike(f"%{search}%")
            )
        
        # Get total count
        count_result = await db.execute(query)
        total = len(count_result.all())
        
        # Add pagination
        offset = (page - 1) * per_page
        query = query.offset(offset).limit(per_page)
        
        # Execute query
        result = await db.execute(query)
        templates = result.scalars().all()
        
        # Convert to response models
        template_responses = [
            TemplateResponse.from_orm(template) for template in templates
        ]
        
        return TemplateList(
            templates=template_responses,
            total=total,
            page=page,
            per_page=per_page
        )
        
    except Exception as e:
        logger.error(f"Error getting templates: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )


@templates_router.post("/", response_model=TemplateResponse)
async def create_template(
    template_data: TemplateCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create new template
    إنشاء قالب جديد
    """
    try:
        # Create new template
        new_template = Template(
            user_id=current_user.id,
            name=template_data.name,
            description=template_data.description,
            title=template_data.title,
            product_description=template_data.product_description,
            category=template_data.category.value,
            subcategory=template_data.subcategory,
            price=template_data.price,
            currency=template_data.currency,
            is_negotiable=template_data.is_negotiable,
            condition=template_data.condition,
            brand=template_data.brand,
            model=template_data.model,
            color=template_data.color,
            size=template_data.size,
            location=template_data.location,
            city=template_data.city,
            state=template_data.state,
            country=template_data.country,
            zip_code=template_data.zip_code,
            image_urls=template_data.image_urls,
            keywords=template_data.keywords,
            tags=template_data.tags,
            contact_method=template_data.contact_method,
            phone_number=template_data.phone_number,
            email=template_data.email
        )
        
        # Set main image if images provided
        if template_data.image_urls and len(template_data.image_urls) > 0:
            new_template.main_image_url = template_data.image_urls[0]
        
        db.add(new_template)
        await db.commit()
        await db.refresh(new_template)
        
        logger.info(f"New template created: {new_template.name} for user {current_user.email}")
        
        return TemplateResponse.from_orm(new_template)
        
    except Exception as e:
        logger.error(f"Error creating template: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )


@templates_router.get("/{template_id}", response_model=TemplateResponse)
async def get_template(
    template_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get specific template
    الحصول على قالب محدد
    """
    try:
        result = await db.execute(
            select(Template).where(
                and_(
                    Template.id == template_id,
                    Template.user_id == current_user.id
                )
            )
        )
        template = result.scalar_one_or_none()
        
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=_("templates.not_found", Language(current_user.language))
            )
        
        return TemplateResponse.from_orm(template)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting template: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )


@templates_router.put("/{template_id}", response_model=TemplateResponse)
async def update_template(
    template_id: str,
    template_data: TemplateUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update template
    تحديث القالب
    """
    try:
        result = await db.execute(
            select(Template).where(
                and_(
                    Template.id == template_id,
                    Template.user_id == current_user.id
                )
            )
        )
        template = result.scalar_one_or_none()
        
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=_("templates.not_found", Language(current_user.language))
            )
        
        # Update fields
        update_data = template_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(template, field):
                if field == "category" and value:
                    setattr(template, field, value.value)
                elif field == "status" and value:
                    setattr(template, field, value.value)
                else:
                    setattr(template, field, value)
        
        # Update main image if images changed
        if template_data.image_urls and len(template_data.image_urls) > 0:
            template.main_image_url = template_data.image_urls[0]
        
        await db.commit()
        await db.refresh(template)
        
        logger.info(f"Template updated: {template.name}")
        
        return TemplateResponse.from_orm(template)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating template: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )


@templates_router.delete("/{template_id}")
async def delete_template(
    template_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete template
    حذف القالب
    """
    try:
        result = await db.execute(
            select(Template).where(
                and_(
                    Template.id == template_id,
                    Template.user_id == current_user.id
                )
            )
        )
        template = result.scalar_one_or_none()
        
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=_("templates.not_found", Language(current_user.language))
            )
        
        # Soft delete
        template.deleted_at = datetime.utcnow()
        
        await db.commit()
        
        logger.info(f"Template deleted: {template.name}")
        
        return {"message": _("templates.deleted_successfully", Language(current_user.language))}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting template: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )


@templates_router.post("/{template_id}/clone", response_model=TemplateResponse)
async def clone_template(
    template_id: str,
    new_name: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Clone template
    استنساخ القالب
    """
    try:
        result = await db.execute(
            select(Template).where(
                and_(
                    Template.id == template_id,
                    Template.user_id == current_user.id
                )
            )
        )
        template = result.scalar_one_or_none()
        
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=_("templates.not_found", Language(current_user.language))
            )
        
        # Clone template
        cloned_template = template.clone(new_name)
        cloned_template.user_id = current_user.id
        
        db.add(cloned_template)
        await db.commit()
        await db.refresh(cloned_template)
        
        logger.info(f"Template cloned: {template.name} -> {cloned_template.name}")
        
        return TemplateResponse.from_orm(cloned_template)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cloning template: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )
