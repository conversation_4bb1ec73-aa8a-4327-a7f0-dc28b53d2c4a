"""
Facebook Marketplace Desktop Application - Main FastAPI Application
منصة إدارة فيسبوك ماركت بليس - التطبيق الرئيسي
"""

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import logging
from loguru import logger

from app.core.config import settings
from app.core.database import engine, Base
from app.api.auth import auth_router
from app.api.accounts import accounts_router
from app.api.templates import templates_router
from app.api.campaigns import campaigns_router
from app.api.analytics import analytics_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logger.info("🚀 Starting Facebook Marketplace Platform...")
    logger.info("🚀 بدء تشغيل منصة فيسبوك ماركت بليس...")
    
    # Create database tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    logger.info("✅ Database initialized")
    logger.info("✅ تم تهيئة قاعدة البيانات")
    
    yield
    
    # Shutdown
    logger.info("🛑 Shutting down Facebook Marketplace Platform...")
    logger.info("🛑 إيقاف تشغيل منصة فيسبوك ماركت بليس...")


# Create FastAPI application
app = FastAPI(
    title="Facebook Marketplace Desktop Application",
    description="منصة إدارة فيسبوك ماركت بليس الاحترافية",
    version="1.0.0",
    docs_url="/docs" if settings.ENVIRONMENT == "development" else None,
    redoc_url="/redoc" if settings.ENVIRONMENT == "development" else None,
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add trusted host middleware
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS
)


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler"""
    logger.error(f"Global exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "message": "Internal server error",
            "message_ar": "خطأ داخلي في الخادم",
            "detail": str(exc) if settings.ENVIRONMENT == "development" else None
        }
    )


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "message": "Facebook Marketplace Platform is running",
        "message_ar": "منصة فيسبوك ماركت بليس تعمل بشكل طبيعي"
    }


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Welcome to Facebook Marketplace Desktop Application",
        "message_ar": "مرحباً بك في تطبيق فيسبوك ماركت بليس",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }


# Include API routers
app.include_router(auth_router, prefix="/api/v1/auth", tags=["Authentication"])
app.include_router(accounts_router, prefix="/api/v1/accounts", tags=["Accounts"])
app.include_router(templates_router, prefix="/api/v1/templates", tags=["Templates"])
app.include_router(campaigns_router, prefix="/api/v1/campaigns", tags=["Campaigns"])
app.include_router(analytics_router, prefix="/api/v1/analytics", tags=["Analytics"])


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True if settings.ENVIRONMENT == "development" else False
    )
