"""
Campaigns API endpoints
نقاط نهاية API للحملات
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
import logging

from app.core.database import get_db
from app.core.i18n import _, Language
from app.models.user import User
from app.models.campaign import Campaign, CampaignStatus, CampaignType
from app.api.auth import get_current_active_user

logger = logging.getLogger(__name__)

# Create router
campaigns_router = APIRouter()


# Pydantic models
class CampaignCreate(BaseModel):
    name: str
    description: Optional[str] = None
    campaign_type: CampaignType = CampaignType.SINGLE_POST
    template_id: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    timezone: str = "UTC"
    post_interval_minutes: int = 60
    posts_per_day: int = 5
    posts_per_hour: int = 1
    target_facebook_accounts: Optional[List[str]] = None
    use_all_accounts: bool = False
    max_posts: Optional[int] = None
    use_content_variations: bool = True
    randomize_posting_times: bool = True
    min_delay_seconds: int = 300
    max_delay_seconds: int = 1800


class CampaignUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    post_interval_minutes: Optional[int] = None
    posts_per_day: Optional[int] = None
    posts_per_hour: Optional[int] = None
    target_facebook_accounts: Optional[List[str]] = None
    use_all_accounts: Optional[bool] = None
    max_posts: Optional[int] = None
    use_content_variations: Optional[bool] = None
    randomize_posting_times: Optional[bool] = None
    min_delay_seconds: Optional[int] = None
    max_delay_seconds: Optional[int] = None
    status: Optional[CampaignStatus] = None


class CampaignResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    campaign_type: str
    status: str
    template_id: Optional[str]
    start_date: Optional[datetime]
    end_date: Optional[datetime]
    timezone: str
    post_interval_minutes: int
    posts_per_day: int
    posts_per_hour: int
    target_facebook_accounts: Optional[List[str]]
    use_all_accounts: bool
    max_posts: Optional[int]
    total_posts_planned: int
    total_posts_completed: int
    successful_posts: int
    failed_posts: int
    pending_posts: int
    progress_percentage: float
    success_rate: float
    use_content_variations: bool
    randomize_posting_times: bool
    min_delay_seconds: int
    max_delay_seconds: int
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    next_execution_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class CampaignList(BaseModel):
    campaigns: List[CampaignResponse]
    total: int
    page: int
    per_page: int


@campaigns_router.get("/", response_model=CampaignList)
async def get_campaigns(
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    status: Optional[CampaignStatus] = None,
    campaign_type: Optional[CampaignType] = None,
    search: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get user's campaigns
    الحصول على حملات المستخدم
    """
    try:
        # Build query
        query = select(Campaign).where(Campaign.user_id == current_user.id)
        
        # Add filters
        if status:
            query = query.where(Campaign.status == status.value)
        
        if campaign_type:
            query = query.where(Campaign.campaign_type == campaign_type.value)
        
        if search:
            query = query.where(
                Campaign.name.ilike(f"%{search}%") |
                Campaign.description.ilike(f"%{search}%")
            )
        
        # Get total count
        count_result = await db.execute(query)
        total = len(count_result.all())
        
        # Add pagination
        offset = (page - 1) * per_page
        query = query.offset(offset).limit(per_page)
        
        # Execute query
        result = await db.execute(query)
        campaigns = result.scalars().all()
        
        # Convert to response models
        campaign_responses = [
            CampaignResponse.from_orm(campaign) for campaign in campaigns
        ]
        
        return CampaignList(
            campaigns=campaign_responses,
            total=total,
            page=page,
            per_page=per_page
        )
        
    except Exception as e:
        logger.error(f"Error getting campaigns: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )


@campaigns_router.post("/", response_model=CampaignResponse)
async def create_campaign(
    campaign_data: CampaignCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create new campaign
    إنشاء حملة جديدة
    """
    try:
        # Create new campaign
        new_campaign = Campaign(
            user_id=current_user.id,
            name=campaign_data.name,
            description=campaign_data.description,
            campaign_type=campaign_data.campaign_type.value,
            template_id=campaign_data.template_id,
            start_date=campaign_data.start_date,
            end_date=campaign_data.end_date,
            timezone=campaign_data.timezone,
            post_interval_minutes=campaign_data.post_interval_minutes,
            posts_per_day=campaign_data.posts_per_day,
            posts_per_hour=campaign_data.posts_per_hour,
            target_facebook_accounts=campaign_data.target_facebook_accounts,
            use_all_accounts=campaign_data.use_all_accounts,
            max_posts=campaign_data.max_posts,
            use_content_variations=campaign_data.use_content_variations,
            randomize_posting_times=campaign_data.randomize_posting_times,
            min_delay_seconds=campaign_data.min_delay_seconds,
            max_delay_seconds=campaign_data.max_delay_seconds
        )
        
        # Calculate total posts planned
        if campaign_data.max_posts:
            new_campaign.total_posts_planned = campaign_data.max_posts
        else:
            # Calculate based on duration and frequency
            if campaign_data.start_date and campaign_data.end_date:
                duration_days = (campaign_data.end_date - campaign_data.start_date).days
                new_campaign.total_posts_planned = duration_days * campaign_data.posts_per_day
            else:
                new_campaign.total_posts_planned = campaign_data.posts_per_day
        
        db.add(new_campaign)
        await db.commit()
        await db.refresh(new_campaign)
        
        logger.info(f"New campaign created: {new_campaign.name} for user {current_user.email}")
        
        return CampaignResponse.from_orm(new_campaign)
        
    except Exception as e:
        logger.error(f"Error creating campaign: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )


@campaigns_router.get("/{campaign_id}", response_model=CampaignResponse)
async def get_campaign(
    campaign_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get specific campaign
    الحصول على حملة محددة
    """
    try:
        result = await db.execute(
            select(Campaign).where(
                and_(
                    Campaign.id == campaign_id,
                    Campaign.user_id == current_user.id
                )
            )
        )
        campaign = result.scalar_one_or_none()
        
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=_("campaigns.not_found", Language(current_user.language))
            )
        
        return CampaignResponse.from_orm(campaign)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting campaign: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )


@campaigns_router.put("/{campaign_id}", response_model=CampaignResponse)
async def update_campaign(
    campaign_id: str,
    campaign_data: CampaignUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update campaign
    تحديث الحملة
    """
    try:
        result = await db.execute(
            select(Campaign).where(
                and_(
                    Campaign.id == campaign_id,
                    Campaign.user_id == current_user.id
                )
            )
        )
        campaign = result.scalar_one_or_none()
        
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=_("campaigns.not_found", Language(current_user.language))
            )
        
        # Check if campaign can be updated
        if campaign.is_running:
            # Only allow certain fields to be updated while running
            allowed_fields = ["posts_per_day", "posts_per_hour", "min_delay_seconds", "max_delay_seconds"]
            update_data = {k: v for k, v in campaign_data.dict(exclude_unset=True).items() if k in allowed_fields}
        else:
            update_data = campaign_data.dict(exclude_unset=True)
        
        # Update fields
        for field, value in update_data.items():
            if hasattr(campaign, field):
                if field == "status" and value:
                    setattr(campaign, field, value.value)
                else:
                    setattr(campaign, field, value)
        
        await db.commit()
        await db.refresh(campaign)
        
        logger.info(f"Campaign updated: {campaign.name}")
        
        return CampaignResponse.from_orm(campaign)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating campaign: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )


@campaigns_router.delete("/{campaign_id}")
async def delete_campaign(
    campaign_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete campaign
    حذف الحملة
    """
    try:
        result = await db.execute(
            select(Campaign).where(
                and_(
                    Campaign.id == campaign_id,
                    Campaign.user_id == current_user.id
                )
            )
        )
        campaign = result.scalar_one_or_none()
        
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=_("campaigns.not_found", Language(current_user.language))
            )
        
        # Check if campaign can be deleted
        if campaign.is_running:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=_("campaigns.cannot_delete_running", Language(current_user.language))
            )
        
        # Soft delete
        campaign.deleted_at = datetime.utcnow()
        
        await db.commit()
        
        logger.info(f"Campaign deleted: {campaign.name}")
        
        return {"message": _("campaigns.deleted_successfully", Language(current_user.language))}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting campaign: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )


@campaigns_router.post("/{campaign_id}/start")
async def start_campaign(
    campaign_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Start campaign
    بدء الحملة
    """
    try:
        result = await db.execute(
            select(Campaign).where(
                and_(
                    Campaign.id == campaign_id,
                    Campaign.user_id == current_user.id
                )
            )
        )
        campaign = result.scalar_one_or_none()
        
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=_("campaigns.not_found", Language(current_user.language))
            )
        
        # Check if campaign can be started
        if campaign.is_running:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=_("campaigns.already_running", Language(current_user.language))
            )
        
        # Start campaign
        campaign.start_campaign()
        await db.commit()
        
        logger.info(f"Campaign started: {campaign.name}")
        
        return {"message": _("campaigns.started_successfully", Language(current_user.language))}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting campaign: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )


@campaigns_router.post("/{campaign_id}/pause")
async def pause_campaign(
    campaign_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Pause campaign
    إيقاف الحملة مؤقتاً
    """
    try:
        result = await db.execute(
            select(Campaign).where(
                and_(
                    Campaign.id == campaign_id,
                    Campaign.user_id == current_user.id
                )
            )
        )
        campaign = result.scalar_one_or_none()
        
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=_("campaigns.not_found", Language(current_user.language))
            )
        
        # Pause campaign
        campaign.pause_campaign()
        await db.commit()
        
        logger.info(f"Campaign paused: {campaign.name}")
        
        return {"message": _("campaigns.paused_successfully", Language(current_user.language))}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error pausing campaign: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", Language(current_user.language))
        )
