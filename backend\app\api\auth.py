"""
Authentication API endpoints
نقاط نهاية API للمصادقة
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime, timedelta
import logging

from app.core.database import get_db
from app.core.security import (
    verify_password, get_password_hash, create_access_token, 
    create_refresh_token, verify_token, create_password_reset_token,
    verify_password_reset_token, create_email_verification_token,
    verify_email_verification_token
)
from app.core.i18n import _, Language
from app.models.user import User, UserStatus, UserRole, SubscriptionPlan

logger = logging.getLogger(__name__)
security = HTTPBearer()

# Create router
auth_router = APIRouter()


# Pydantic models
class UserRegister(BaseModel):
    email: EmailStr
    password: str
    full_name: Optional[str] = None
    language: Optional[Language] = Language.ENGLISH


class UserLogin(BaseModel):
    email: EmailStr
    password: str
    remember_me: Optional[bool] = False


class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: dict


class RefreshTokenRequest(BaseModel):
    refresh_token: str


class PasswordResetRequest(BaseModel):
    email: EmailStr


class PasswordResetConfirm(BaseModel):
    token: str
    new_password: str


class EmailVerificationRequest(BaseModel):
    email: EmailStr


class UserProfile(BaseModel):
    id: str
    email: str
    username: Optional[str]
    full_name: Optional[str]
    role: str
    status: str
    subscription_plan: str
    is_email_verified: bool
    language: str
    created_at: datetime


# Dependency to get current user
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """Get current authenticated user"""
    token = credentials.credentials
    user_id = verify_token(token, "access")
    
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=_("auth.invalid_credentials"),
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Get user from database
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=_("auth.invalid_credentials"),
        )
    
    if user.status == UserStatus.SUSPENDED:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=_("auth.account_disabled"),
        )
    
    return user


async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """Get current active user"""
    if current_user.status != UserStatus.ACTIVE:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=_("auth.account_disabled"),
        )
    return current_user


@auth_router.post("/register", response_model=dict)
async def register(
    user_data: UserRegister,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    Register new user
    تسجيل مستخدم جديد
    """
    try:
        # Check if user already exists
        result = await db.execute(select(User).where(User.email == user_data.email))
        existing_user = result.scalar_one_or_none()
        
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=_("auth.email_already_registered", user_data.language),
            )
        
        # Create new user
        hashed_password = get_password_hash(user_data.password)
        new_user = User(
            email=user_data.email,
            hashed_password=hashed_password,
            full_name=user_data.full_name,
            language=user_data.language.value,
            role=UserRole.VIEWER,
            status=UserStatus.PENDING_VERIFICATION,
            subscription_plan=SubscriptionPlan.STARTER
        )
        
        db.add(new_user)
        await db.commit()
        await db.refresh(new_user)
        
        # Create email verification token
        verification_token = create_email_verification_token(new_user.email)
        new_user.email_verification_token = verification_token
        new_user.email_verification_expires = datetime.utcnow() + timedelta(days=1)
        
        await db.commit()
        
        logger.info(f"New user registered: {new_user.email}")
        
        return {
            "message": _("auth.registration_successful", user_data.language),
            "user_id": str(new_user.id),
            "verification_required": True
        }
        
    except Exception as e:
        logger.error(f"Registration error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error", user_data.language),
        )


@auth_router.post("/login", response_model=TokenResponse)
async def login(
    user_data: UserLogin,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    User login
    تسجيل دخول المستخدم
    """
    try:
        # Get user by email
        result = await db.execute(select(User).where(User.email == user_data.email))
        user = result.scalar_one_or_none()
        
        if not user or not verify_password(user_data.password, user.hashed_password):
            # Record failed login attempt
            if user:
                user.record_failed_login()
                await db.commit()
            
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=_("auth.invalid_credentials"),
            )
        
        # Check if account is locked
        if user.is_locked():
            raise HTTPException(
                status_code=status.HTTP_423_LOCKED,
                detail=_("auth.account_locked"),
            )
        
        # Check account status
        if user.status == UserStatus.SUSPENDED:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=_("auth.account_disabled"),
            )
        
        # Create tokens
        access_token_expires = timedelta(minutes=30)
        if user_data.remember_me:
            access_token_expires = timedelta(days=7)
        
        access_token = create_access_token(
            subject=str(user.id),
            expires_delta=access_token_expires
        )
        refresh_token = create_refresh_token(subject=str(user.id))
        
        # Update login information
        client_ip = request.client.host if request.client else None
        user.update_last_login(client_ip)
        await db.commit()
        
        logger.info(f"User logged in: {user.email}")
        
        return TokenResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=int(access_token_expires.total_seconds()),
            user={
                "id": str(user.id),
                "email": user.email,
                "full_name": user.full_name,
                "role": user.role.value,
                "language": user.language,
                "subscription_plan": user.subscription_plan.value
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error"),
        )


@auth_router.post("/refresh", response_model=dict)
async def refresh_token(
    token_data: RefreshTokenRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Refresh access token
    تحديث رمز الوصول
    """
    try:
        user_id = verify_token(token_data.refresh_token, "refresh")
        
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=_("auth.invalid_token"),
            )
        
        # Get user
        result = await db.execute(select(User).where(User.id == user_id))
        user = result.scalar_one_or_none()
        
        if not user or user.status != UserStatus.ACTIVE:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=_("auth.invalid_token"),
            )
        
        # Create new access token
        access_token = create_access_token(subject=str(user.id))
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": 1800  # 30 minutes
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error"),
        )


@auth_router.get("/me", response_model=UserProfile)
async def get_current_user_profile(
    current_user: User = Depends(get_current_active_user)
):
    """
    Get current user profile
    الحصول على ملف المستخدم الحالي
    """
    return UserProfile(
        id=str(current_user.id),
        email=current_user.email,
        username=current_user.username,
        full_name=current_user.full_name,
        role=current_user.role.value,
        status=current_user.status.value,
        subscription_plan=current_user.subscription_plan.value,
        is_email_verified=current_user.is_email_verified,
        language=current_user.language,
        created_at=current_user.created_at
    )


@auth_router.post("/logout")
async def logout(current_user: User = Depends(get_current_active_user)):
    """
    User logout
    تسجيل خروج المستخدم
    """
    logger.info(f"User logged out: {current_user.email}")
    return {"message": _("auth.logout_successful", Language(current_user.language))}


@auth_router.post("/forgot-password")
async def forgot_password(
    request_data: PasswordResetRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Request password reset
    طلب إعادة تعيين كلمة المرور
    """
    try:
        # Get user by email
        result = await db.execute(select(User).where(User.email == request_data.email))
        user = result.scalar_one_or_none()
        
        if user:
            # Create password reset token
            reset_token = create_password_reset_token(user.email)
            user.password_reset_token = reset_token
            user.password_reset_expires = datetime.utcnow() + timedelta(hours=1)
            await db.commit()
            
            # TODO: Send email with reset link
            logger.info(f"Password reset requested for: {user.email}")
        
        # Always return success to prevent email enumeration
        return {"message": _("auth.password_reset_sent")}
        
    except Exception as e:
        logger.error(f"Password reset error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error"),
        )


@auth_router.post("/reset-password")
async def reset_password(
    reset_data: PasswordResetConfirm,
    db: AsyncSession = Depends(get_db)
):
    """
    Reset password with token
    إعادة تعيين كلمة المرور بالرمز
    """
    try:
        email = verify_password_reset_token(reset_data.token)
        
        if not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=_("auth.invalid_token"),
            )
        
        # Get user
        result = await db.execute(select(User).where(User.email == email))
        user = result.scalar_one_or_none()
        
        if not user or user.password_reset_token != reset_data.token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=_("auth.invalid_token"),
            )
        
        # Update password
        user.hashed_password = get_password_hash(reset_data.new_password)
        user.password_reset_token = None
        user.password_reset_expires = None
        user.failed_login_attempts = 0  # Reset failed attempts
        
        await db.commit()
        
        logger.info(f"Password reset completed for: {user.email}")
        
        return {"message": _("auth.password_reset_successful")}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Password reset confirmation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("common.error"),
        )
