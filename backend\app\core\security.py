"""
Security utilities for authentication and encryption
أدوات الأمان للمصادقة والتشفير
"""

from datetime import datetime, timedelta
from typing import Any, Union, Optional
from jose import JWTError, jwt
from passlib.context import CryptContext
from cryptography.fernet import Fernet
from fastapi import HTT<PERSON><PERSON>xception, status
import secrets
import base64
import hashlib
import hmac

from app.core.config import settings

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Encryption key for sensitive data
ENCRYPTION_KEY = base64.urlsafe_b64encode(
    hashlib.sha256(settings.SECRET_KEY.encode()).digest()
)
cipher_suite = Fernet(ENCRYPTION_KEY)


def create_access_token(
    subject: Union[str, Any], 
    expires_delta: Optional[timedelta] = None
) -> str:
    """
    Create JWT access token
    إنشاء رمز الوصول JWT
    """
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    
    to_encode = {"exp": expire, "sub": str(subject), "type": "access"}
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.SECRET_KEY, 
        algorithm=settings.ALGORITHM
    )
    return encoded_jwt


def create_refresh_token(subject: Union[str, Any]) -> str:
    """
    Create JWT refresh token
    إنشاء رمز التحديث JWT
    """
    expire = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode = {"exp": expire, "sub": str(subject), "type": "refresh"}
    encoded_jwt = jwt.encode(
        to_encode,
        settings.SECRET_KEY,
        algorithm=settings.ALGORITHM
    )
    return encoded_jwt


def verify_token(token: str, token_type: str = "access") -> Optional[str]:
    """
    Verify JWT token and return subject
    التحقق من رمز JWT وإرجاع الموضوع
    """
    try:
        payload = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        
        # Check token type
        if payload.get("type") != token_type:
            return None
            
        # Check expiration
        exp = payload.get("exp")
        if exp is None or datetime.utcnow() > datetime.fromtimestamp(exp):
            return None
            
        subject: str = payload.get("sub")
        return subject
        
    except JWTError:
        return None


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify password against hash
    التحقق من كلمة المرور مقابل التشفير
    """
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """
    Hash password
    تشفير كلمة المرور
    """
    return pwd_context.hash(password)


def encrypt_sensitive_data(data: str) -> str:
    """
    Encrypt sensitive data like Facebook passwords
    تشفير البيانات الحساسة مثل كلمات مرور فيسبوك
    """
    return cipher_suite.encrypt(data.encode()).decode()


def decrypt_sensitive_data(encrypted_data: str) -> str:
    """
    Decrypt sensitive data
    فك تشفير البيانات الحساسة
    """
    return cipher_suite.decrypt(encrypted_data.encode()).decode()


def generate_random_string(length: int = 32) -> str:
    """
    Generate random string for tokens
    توليد نص عشوائي للرموز
    """
    return secrets.token_urlsafe(length)


def generate_api_key() -> str:
    """
    Generate API key for external integrations
    توليد مفتاح API للتكاملات الخارجية
    """
    return f"fbmp_{generate_random_string(32)}"


def verify_api_key(api_key: str, stored_hash: str) -> bool:
    """
    Verify API key against stored hash
    التحقق من مفتاح API مقابل التشفير المخزن
    """
    return hmac.compare_digest(
        hashlib.sha256(api_key.encode()).hexdigest(),
        stored_hash
    )


def hash_api_key(api_key: str) -> str:
    """
    Hash API key for storage
    تشفير مفتاح API للتخزين
    """
    return hashlib.sha256(api_key.encode()).hexdigest()


def create_2fa_secret() -> str:
    """
    Create 2FA secret for TOTP
    إنشاء سر 2FA لـ TOTP
    """
    return base64.b32encode(secrets.token_bytes(20)).decode()


def verify_2fa_token(token: str, secret: str) -> bool:
    """
    Verify 2FA TOTP token
    التحقق من رمز 2FA TOTP
    """
    import pyotp
    totp = pyotp.TOTP(secret)
    return totp.verify(token, valid_window=1)


def create_password_reset_token(email: str) -> str:
    """
    Create password reset token
    إنشاء رمز إعادة تعيين كلمة المرور
    """
    expire = datetime.utcnow() + timedelta(hours=1)
    to_encode = {
        "exp": expire,
        "sub": email,
        "type": "password_reset"
    }
    return jwt.encode(
        to_encode,
        settings.SECRET_KEY,
        algorithm=settings.ALGORITHM
    )


def verify_password_reset_token(token: str) -> Optional[str]:
    """
    Verify password reset token
    التحقق من رمز إعادة تعيين كلمة المرور
    """
    return verify_token(token, "password_reset")


def create_email_verification_token(email: str) -> str:
    """
    Create email verification token
    إنشاء رمز التحقق من البريد الإلكتروني
    """
    expire = datetime.utcnow() + timedelta(days=1)
    to_encode = {
        "exp": expire,
        "sub": email,
        "type": "email_verification"
    }
    return jwt.encode(
        to_encode,
        settings.SECRET_KEY,
        algorithm=settings.ALGORITHM
    )


def verify_email_verification_token(token: str) -> Optional[str]:
    """
    Verify email verification token
    التحقق من رمز التحقق من البريد الإلكتروني
    """
    return verify_token(token, "email_verification")


# Security exceptions
class SecurityException(HTTPException):
    """Base security exception"""
    pass


class InvalidTokenException(SecurityException):
    """Invalid token exception"""
    def __init__(self):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token",
            headers={"WWW-Authenticate": "Bearer"},
        )


class InsufficientPermissionsException(SecurityException):
    """Insufficient permissions exception"""
    def __init__(self):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions"
        )
