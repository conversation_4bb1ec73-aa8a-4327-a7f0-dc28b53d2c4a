# Facebook Marketplace Platform - Environment Variables
# Copy this file to .env and update the values

# =============================================================================
# BASIC SETTINGS
# =============================================================================
PROJECT_NAME="Facebook Marketplace Desktop Application"
PROJECT_NAME_AR="تطبيق فيسبوك ماركت بليس"
VERSION="1.0.0"
ENVIRONMENT="development"  # development, production, testing
DEBUG=true

# =============================================================================
# SECURITY
# =============================================================================
SECRET_KEY="your-super-secret-key-change-in-production-make-it-very-long-and-random"
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
ALGORITHM="HS256"

# =============================================================================
# DATABASE
# =============================================================================
DATABASE_URL="postgresql://postgres:postgres123@localhost:5432/facebook_marketplace"
DATABASE_URL_ASYNC="postgresql+asyncpg://postgres:postgres123@localhost:5432/facebook_marketplace"

# =============================================================================
# REDIS
# =============================================================================
REDIS_URL="redis://localhost:6379"
REDIS_PASSWORD=""

# =============================================================================
# CORS SETTINGS
# =============================================================================
ALLOWED_HOSTS="*"
ALLOWED_ORIGINS="http://localhost:3000,http://127.0.0.1:3000,http://localhost:8000,http://127.0.0.1:8000"

# =============================================================================
# FILE STORAGE
# =============================================================================
UPLOAD_DIR="uploads"
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_FILE_TYPES="jpg,jpeg,png,gif,webp"

# =============================================================================
# BROWSER AUTOMATION
# =============================================================================
BROWSER_PROFILES_DIR="browser_profiles"
BROWSER_TIMEOUT=30000  # 30 seconds in milliseconds
BROWSER_HEADLESS=true
BROWSER_SLOW_MO=100  # milliseconds

# =============================================================================
# ANTI-BAN SETTINGS
# =============================================================================
MIN_DELAY=5  # seconds
MAX_DELAY=15  # seconds
HUMAN_TYPING_SPEED=100  # milliseconds per character
MOUSE_MOVEMENT_SPEED=1000  # milliseconds

# =============================================================================
# FACEBOOK SETTINGS
# =============================================================================
FB_LOGIN_URL="https://www.facebook.com/login"
FB_MARKETPLACE_URL="https://www.facebook.com/marketplace"
FB_MAX_DAILY_POSTS=50
FB_MAX_HOURLY_POSTS=5

# =============================================================================
# AI CONTENT GENERATION (Optional)
# =============================================================================
OPENAI_API_KEY=""  # Your OpenAI API key
ANTHROPIC_API_KEY=""  # Your Anthropic API key
AI_MODEL="gpt-3.5-turbo"
AI_MAX_TOKENS=1000
AI_TEMPERATURE=0.7

# =============================================================================
# EMAIL SETTINGS (Optional)
# =============================================================================
SMTP_HOST=""  # e.g., smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=""  # Your email
SMTP_PASSWORD=""  # Your email password or app password
SMTP_TLS=true

# =============================================================================
# LOGGING
# =============================================================================
LOG_LEVEL="INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FILE="logs/app.log"
LOG_ROTATION="1 day"
LOG_RETENTION="30 days"

# =============================================================================
# CELERY (Background Tasks)
# =============================================================================
CELERY_BROKER_URL="redis://localhost:6379/0"
CELERY_RESULT_BACKEND="redis://localhost:6379/0"
CELERY_TASK_SERIALIZER="json"
CELERY_RESULT_SERIALIZER="json"
CELERY_ACCEPT_CONTENT="json"
CELERY_TIMEZONE="UTC"
CELERY_ENABLE_UTC=true

# =============================================================================
# SUBSCRIPTION PLANS
# =============================================================================
STARTER_PLAN_ACCOUNTS=5
STARTER_PLAN_POSTS=100
PROFESSIONAL_PLAN_ACCOUNTS=25
PROFESSIONAL_PLAN_POSTS=1000
ENTERPRISE_PLAN_ACCOUNTS=-1  # Unlimited
ENTERPRISE_PLAN_POSTS=-1  # Unlimited

# =============================================================================
# RATE LIMITING
# =============================================================================
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000
RATE_LIMIT_PER_DAY=10000

# =============================================================================
# MONITORING (Optional)
# =============================================================================
SENTRY_DSN=""  # Your Sentry DSN for error tracking
ENABLE_METRICS=true
METRICS_PORT=9090

# =============================================================================
# INTERNATIONALIZATION
# =============================================================================
DEFAULT_LANGUAGE="en"  # en, ar
SUPPORTED_LANGUAGES="en,ar"
TIMEZONE="UTC"

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Only used in development environment
DEV_RELOAD=true
DEV_PORT=8000
DEV_HOST="0.0.0.0"

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================
# Only used in production environment
PROD_WORKERS=4
PROD_MAX_REQUESTS=1000
PROD_MAX_REQUESTS_JITTER=100
PROD_TIMEOUT=30
PROD_KEEPALIVE=2

# =============================================================================
# SSL/TLS SETTINGS (Production)
# =============================================================================
SSL_KEYFILE=""  # Path to SSL key file
SSL_CERTFILE=""  # Path to SSL certificate file
SSL_CA_CERTS=""  # Path to CA certificates file

# =============================================================================
# BACKUP SETTINGS (Optional)
# =============================================================================
BACKUP_ENABLED=false
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM (cron format)
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH="backups"

# =============================================================================
# PROXY SETTINGS (Optional)
# =============================================================================
DEFAULT_PROXY_ENABLED=false
DEFAULT_PROXY_SERVER=""  # e.g., http://proxy.example.com:8080
DEFAULT_PROXY_USERNAME=""
DEFAULT_PROXY_PASSWORD=""
DEFAULT_PROXY_TYPE="http"  # http, https, socks4, socks5

# =============================================================================
# WEBHOOK SETTINGS (Optional)
# =============================================================================
WEBHOOK_ENABLED=false
WEBHOOK_URL=""  # URL to send webhook notifications
WEBHOOK_SECRET=""  # Secret for webhook verification
WEBHOOK_EVENTS="campaign.started,campaign.completed,post.failed"

# =============================================================================
# ANALYTICS SETTINGS
# =============================================================================
ANALYTICS_ENABLED=true
ANALYTICS_RETENTION_DAYS=365
ANALYTICS_AGGREGATION_INTERVAL=3600  # 1 hour in seconds

# =============================================================================
# CACHE SETTINGS
# =============================================================================
CACHE_TTL=300  # 5 minutes in seconds
CACHE_MAX_SIZE=1000  # Maximum number of cached items
CACHE_ENABLED=true

# =============================================================================
# API SETTINGS
# =============================================================================
API_V1_PREFIX="/api/v1"
API_DOCS_ENABLED=true  # Set to false in production
API_REDOC_ENABLED=true  # Set to false in production
API_OPENAPI_URL="/openapi.json"

# =============================================================================
# FEATURE FLAGS
# =============================================================================
FEATURE_AI_CONTENT=true
FEATURE_PROXY_ROTATION=true
FEATURE_ADVANCED_ANALYTICS=true
FEATURE_WEBHOOK_NOTIFICATIONS=false
FEATURE_BACKUP_AUTOMATION=false
FEATURE_MULTI_LANGUAGE=true
FEATURE_DARK_MODE=true
FEATURE_2FA=true

# =============================================================================
# TESTING SETTINGS
# =============================================================================
TEST_DATABASE_URL="sqlite:///./test.db"
TEST_REDIS_URL="redis://localhost:6379/1"
TEST_SKIP_AUTH=false
TEST_MOCK_FACEBOOK=true
