"""
Facebook Account model for managing Facebook accounts
نموذج حساب فيسبوك لإدارة حسابات فيسبوك
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid
import enum
from datetime import datetime, timedelta

from app.core.database import Base


class AccountStatus(str, enum.Enum):
    """Facebook account status"""
    ACTIVE = "active"
    SUSPENDED = "suspended"
    BANNED = "banned"
    VERIFICATION_REQUIRED = "verification_required"
    CHECKPOINT = "checkpoint"
    DISABLED = "disabled"


class LoginMethod(str, enum.Enum):
    """Login method for Facebook account"""
    EMAIL_PASSWORD = "email_password"
    ACCESS_TOKEN = "access_token"
    BROWSER_PROFILE = "browser_profile"
    COOKIES = "cookies"


class FacebookAccount(Base):
    """Facebook Account model"""
    __tablename__ = "facebook_accounts"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Foreign key to user
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    
    # Account identification
    account_name = Column(String(255), nullable=False)
    facebook_id = Column(String(100), nullable=True, index=True)
    email = Column(String(255), nullable=False)
    username = Column(String(100), nullable=True)
    
    # Login credentials (encrypted)
    encrypted_password = Column(Text, nullable=True)
    access_token = Column(Text, nullable=True)
    refresh_token = Column(Text, nullable=True)
    
    # Account status
    status = Column(String(50), default=AccountStatus.ACTIVE.value, nullable=False)
    login_method = Column(String(50), default=LoginMethod.EMAIL_PASSWORD.value, nullable=False)
    
    # Profile information
    profile_name = Column(String(255), nullable=True)
    profile_picture_url = Column(String(500), nullable=True)
    profile_url = Column(String(500), nullable=True)
    
    # Account settings
    is_business_account = Column(Boolean, default=False, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    is_2fa_enabled = Column(Boolean, default=False, nullable=False)
    
    # Browser and session data
    browser_profile_path = Column(String(500), nullable=True)
    cookies_data = Column(JSON, nullable=True)
    user_agent = Column(String(500), nullable=True)
    proxy_settings = Column(JSON, nullable=True)
    
    # Activity tracking
    last_login_at = Column(DateTime(timezone=True), nullable=True)
    last_activity_at = Column(DateTime(timezone=True), nullable=True)
    last_post_at = Column(DateTime(timezone=True), nullable=True)
    
    # Usage statistics
    total_posts = Column(Integer, default=0, nullable=False)
    successful_posts = Column(Integer, default=0, nullable=False)
    failed_posts = Column(Integer, default=0, nullable=False)
    daily_posts_count = Column(Integer, default=0, nullable=False)
    last_daily_reset = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Security and anti-ban
    ban_count = Column(Integer, default=0, nullable=False)
    last_ban_date = Column(DateTime(timezone=True), nullable=True)
    checkpoint_count = Column(Integer, default=0, nullable=False)
    last_checkpoint_date = Column(DateTime(timezone=True), nullable=True)
    
    # Rate limiting
    posts_today = Column(Integer, default=0, nullable=False)
    posts_this_hour = Column(Integer, default=0, nullable=False)
    last_post_time = Column(DateTime(timezone=True), nullable=True)
    
    # Account health score (0-100)
    health_score = Column(Integer, default=100, nullable=False)
    risk_level = Column(String(20), default="low", nullable=False)  # low, medium, high
    
    # Marketplace specific
    marketplace_access = Column(Boolean, default=True, nullable=False)
    marketplace_banned = Column(Boolean, default=False, nullable=False)
    marketplace_ban_date = Column(DateTime(timezone=True), nullable=True)
    
    # Notes and comments
    notes = Column(Text, nullable=True)
    admin_notes = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="facebook_accounts")
    sessions = relationship("BrowserSession", back_populates="facebook_account", cascade="all, delete-orphan")
    posts = relationship("Post", back_populates="facebook_account", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<FacebookAccount(id={self.id}, email={self.email}, status={self.status})>"
    
    @property
    def is_active(self) -> bool:
        """Check if account is active"""
        return self.status == AccountStatus.ACTIVE.value
    
    @property
    def is_banned(self) -> bool:
        """Check if account is banned"""
        return self.status == AccountStatus.BANNED.value
    
    @property
    def needs_verification(self) -> bool:
        """Check if account needs verification"""
        return self.status == AccountStatus.VERIFICATION_REQUIRED.value
    
    @property
    def is_in_checkpoint(self) -> bool:
        """Check if account is in checkpoint"""
        return self.status == AccountStatus.CHECKPOINT.value
    
    @property
    def success_rate(self) -> float:
        """Calculate post success rate"""
        if self.total_posts == 0:
            return 0.0
        return (self.successful_posts / self.total_posts) * 100
    
    @property
    def can_post_today(self) -> bool:
        """Check if account can post today"""
        from app.core.config import settings
        return self.posts_today < settings.FB_MAX_DAILY_POSTS
    
    @property
    def can_post_this_hour(self) -> bool:
        """Check if account can post this hour"""
        from app.core.config import settings
        return self.posts_this_hour < settings.FB_MAX_HOURLY_POSTS
    
    def reset_daily_counters(self):
        """Reset daily post counters"""
        now = datetime.utcnow()
        if self.last_daily_reset.date() < now.date():
            self.posts_today = 0
            self.last_daily_reset = now
    
    def reset_hourly_counters(self):
        """Reset hourly post counters"""
        now = datetime.utcnow()
        if self.last_post_time:
            time_diff = now - self.last_post_time
            if time_diff >= timedelta(hours=1):
                self.posts_this_hour = 0
    
    def increment_post_counters(self):
        """Increment post counters"""
        self.reset_daily_counters()
        self.reset_hourly_counters()
        
        self.total_posts += 1
        self.posts_today += 1
        self.posts_this_hour += 1
        self.last_post_time = datetime.utcnow()
        self.last_activity_at = datetime.utcnow()
        self.last_post_at = datetime.utcnow()
    
    def record_successful_post(self):
        """Record successful post"""
        self.successful_posts += 1
        self.update_health_score(5)  # Increase health score
    
    def record_failed_post(self):
        """Record failed post"""
        self.failed_posts += 1
        self.update_health_score(-10)  # Decrease health score
    
    def record_ban(self):
        """Record account ban"""
        self.status = AccountStatus.BANNED.value
        self.ban_count += 1
        self.last_ban_date = datetime.utcnow()
        self.health_score = 0
        self.risk_level = "high"
    
    def record_checkpoint(self):
        """Record checkpoint"""
        self.status = AccountStatus.CHECKPOINT.value
        self.checkpoint_count += 1
        self.last_checkpoint_date = datetime.utcnow()
        self.update_health_score(-20)
    
    def update_health_score(self, change: int):
        """Update account health score"""
        self.health_score = max(0, min(100, self.health_score + change))
        
        # Update risk level based on health score
        if self.health_score >= 80:
            self.risk_level = "low"
        elif self.health_score >= 50:
            self.risk_level = "medium"
        else:
            self.risk_level = "high"
    
    def update_last_login(self):
        """Update last login time"""
        self.last_login_at = datetime.utcnow()
        self.last_activity_at = datetime.utcnow()
    
    def update_last_activity(self):
        """Update last activity time"""
        self.last_activity_at = datetime.utcnow()
    
    def is_ready_for_posting(self) -> bool:
        """Check if account is ready for posting"""
        return (
            self.is_active and
            self.marketplace_access and
            not self.marketplace_banned and
            self.can_post_today and
            self.can_post_this_hour and
            self.health_score > 30
        )
    
    def get_next_available_post_time(self) -> datetime:
        """Get next available time for posting"""
        from app.core.config import settings
        
        if not self.last_post_time:
            return datetime.utcnow()
        
        # Add minimum delay between posts
        next_time = self.last_post_time + timedelta(seconds=settings.MIN_DELAY)
        
        # If we've reached hourly limit, wait until next hour
        if not self.can_post_this_hour:
            next_hour = self.last_post_time.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
            next_time = max(next_time, next_hour)
        
        # If we've reached daily limit, wait until next day
        if not self.can_post_today:
            next_day = self.last_post_time.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
            next_time = max(next_time, next_day)
        
        return next_time
