{"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "add": "Add", "create": "Create", "update": "Update", "remove": "Remove", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "clear": "Clear", "refresh": "Refresh", "retry": "Retry", "download": "Download", "upload": "Upload", "copy": "Copy", "paste": "Paste", "cut": "Cut", "select": "Select", "selectAll": "Select All", "none": "None", "all": "All", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "online": "Online", "offline": "Offline", "connected": "Connected", "disconnected": "Disconnected", "success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "pending": "Pending", "completed": "Completed", "failed": "Failed", "cancelled": "Cancelled", "draft": "Draft", "published": "Published", "archived": "Archived"}, "navigation": {"dashboard": "Dashboard", "accounts": "Facebook Accounts", "templates": "Templates", "campaigns": "Campaigns", "analytics": "Analytics", "settings": "Settings", "profile": "Profile", "logout": "Logout"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "fullName": "Full Name", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "resetPassword": "Reset Password", "newPassword": "New Password", "currentPassword": "Current Password", "loginTitle": "Sign in to your account", "loginSubtitle": "Welcome back to Facebook Marketplace Platform", "registerTitle": "Create your account", "registerSubtitle": "Join Facebook Marketplace Platform today", "forgotPasswordTitle": "Reset your password", "forgotPasswordSubtitle": "Enter your email to receive reset instructions", "loginSuccess": "Login successful!", "registerSuccess": "Registration successful! Please check your email.", "logoutSuccess": "Logged out successfully", "invalidCredentials": "Invalid email or password", "emailRequired": "Email is required", "passwordRequired": "Password is required", "passwordMinLength": "Password must be at least 8 characters", "passwordsDoNotMatch": "Passwords do not match", "emailInvalid": "Please enter a valid email address", "accountLocked": "Account is locked due to too many failed attempts", "accountDisabled": "Account is disabled", "emailNotVerified": "Email is not verified", "tokenExpired": "Token has expired", "tokenInvalid": "Invalid token", "passwordResetSent": "Password reset instructions sent to your email", "passwordResetSuccess": "Password reset successful"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome to Facebook Marketplace Platform", "overview": "Overview", "totalAccounts": "Total Accounts", "activeAccounts": "Active Accounts", "totalTemplates": "Total Templates", "activeCampaigns": "Active Campaigns", "totalPosts": "Total Posts", "successfulPosts": "Successful Posts", "failedPosts": "Failed Posts", "successRate": "Success Rate", "postsToday": "Posts Today", "postsThisWeek": "Posts This Week", "postsThisMonth": "Posts This Month", "recentActivity": "Recent Activity", "quickActions": "Quick Actions", "addAccount": "Add Account", "createTemplate": "Create Template", "startCampaign": "Start Campaign", "viewAnalytics": "View Analytics"}, "accounts": {"title": "Facebook Accounts", "addAccount": "Add Account", "editAccount": "Edit Account", "deleteAccount": "Delete Account", "accountName": "Account Name", "email": "Email", "status": "Status", "lastLogin": "Last Login", "lastActivity": "Last Activity", "totalPosts": "Total Posts", "successRate": "Success Rate", "healthScore": "Health Score", "riskLevel": "Risk Level", "actions": "Actions", "loginMethod": "Login Method", "emailPassword": "Email & Password", "accessToken": "Access Token", "browserProfile": "Browser Profile", "cookies": "Cookies", "testLogin": "Test Login", "viewDetails": "View Details", "accountAdded": "Account added successfully", "accountUpdated": "Account updated successfully", "accountDeleted": "Account deleted successfully", "loginTestSuccess": "Login test successful", "loginTestFailed": "Login test failed", "confirmDelete": "Are you sure you want to delete this account?", "noAccounts": "No Facebook accounts found", "addFirstAccount": "Add your first Facebook account to get started"}, "templates": {"title": "Product Templates", "addTemplate": "Add Template", "editTemplate": "Edit Template", "deleteTemplate": "Delete Template", "cloneTemplate": "<PERSON><PERSON> Template", "templateName": "Template Name", "category": "Category", "price": "Price", "description": "Description", "condition": "Condition", "brand": "Brand", "model": "Model", "color": "Color", "size": "Size", "location": "Location", "images": "Images", "keywords": "Keywords", "tags": "Tags", "timesUsed": "Times Used", "lastUsed": "Last Used", "templateAdded": "Template added successfully", "templateUpdated": "Template updated successfully", "templateDeleted": "Template deleted successfully", "templateCloned": "Template cloned successfully", "confirmDelete": "Are you sure you want to delete this template?", "noTemplates": "No templates found", "createFirstTemplate": "Create your first template to get started"}, "campaigns": {"title": "Campaigns", "addCampaign": "Add Campaign", "editCampaign": "Edit Campaign", "deleteCampaign": "Delete Campaign", "startCampaign": "Start Campaign", "pauseCampaign": "Pause Campaign", "resumeCampaign": "Resume Campaign", "stopCampaign": "Stop Campaign", "campaignName": "Campaign Name", "campaignType": "Campaign Type", "template": "Template", "startDate": "Start Date", "endDate": "End Date", "status": "Status", "progress": "Progress", "postsPlanned": "Posts Planned", "postsCompleted": "Posts Completed", "successfulPosts": "Successful Posts", "failedPosts": "Failed Posts", "targetAccounts": "Target Accounts", "useAllAccounts": "Use All Accounts", "postInterval": "Post Interval (minutes)", "postsPerDay": "Posts Per Day", "postsPerHour": "Posts Per Hour", "maxPosts": "<PERSON>s", "randomizeContent": "Randomize Content", "randomizeTiming": "Randomize Timing", "minDelay": "<PERSON> (seconds)", "maxDelay": "<PERSON> (seconds)", "campaignAdded": "Campaign added successfully", "campaignUpdated": "Campaign updated successfully", "campaignDeleted": "Campaign deleted successfully", "campaignStarted": "Campaign started successfully", "campaignPaused": "Campaign paused successfully", "campaignResumed": "Campaign resumed successfully", "campaignStopped": "Campaign stopped successfully", "confirmDelete": "Are you sure you want to delete this campaign?", "confirmStart": "Are you sure you want to start this campaign?", "confirmPause": "Are you sure you want to pause this campaign?", "confirmStop": "Are you sure you want to stop this campaign?", "noCampaigns": "No campaigns found", "createFirstCampaign": "Create your first campaign to get started"}, "analytics": {"title": "Analytics", "overview": "Overview", "performance": "Performance", "trends": "Trends", "reports": "Reports", "accountPerformance": "Account Performance", "templatePerformance": "Template Performance", "campaignPerformance": "Campaign Performance", "categoryPerformance": "Category Performance", "postsOverTime": "Posts Over Time", "successRate": "Success Rate", "engagementRate": "Engagement Rate", "views": "Views", "clicks": "<PERSON>licks", "messages": "Messages", "saves": "Saves", "shares": "Shares", "averageViews": "Average Views", "averageMessages": "Average Messages", "topPerforming": "Top Performing", "worstPerforming": "Worst Performing", "last7Days": "Last 7 Days", "last30Days": "Last 30 Days", "last90Days": "Last 90 Days", "customRange": "Custom Range", "exportReport": "Export Report", "generateReport": "Generate Report", "noData": "No data available for the selected period"}, "settings": {"title": "Settings", "profile": "Profile", "security": "Security", "notifications": "Notifications", "preferences": "Preferences", "subscription": "Subscription", "billing": "Billing", "api": "API Access", "general": "General", "language": "Language", "theme": "Theme", "timezone": "Timezone", "lightTheme": "Light", "darkTheme": "Dark", "autoTheme": "Auto", "english": "English", "arabic": "العربية", "emailNotifications": "Email Notifications", "pushNotifications": "Push Notifications", "campaignNotifications": "Campaign Notifications", "errorNotifications": "Error Notifications", "weeklyReports": "Weekly Reports", "monthlyReports": "Monthly Reports", "changePassword": "Change Password", "enable2FA": "Enable Two-Factor Authentication", "disable2FA": "Disable Two-Factor Authentication", "generateApiKey": "Generate API Key", "revokeApiKey": "Revoke API Key", "downloadData": "Download My Data", "deleteAccount": "Delete Account", "settingsUpdated": "Settings updated successfully", "passwordChanged": "Password changed successfully", "2faEnabled": "Two-factor authentication enabled", "2faDisabled": "Two-factor authentication disabled", "apiKeyGenerated": "API key generated successfully", "apiKeyRevoked": "API key revoked successfully", "confirmDeleteAccount": "Are you sure you want to delete your account? This action cannot be undone."}, "errors": {"generic": "An error occurred. Please try again.", "network": "Network error. Please check your connection.", "unauthorized": "You are not authorized to perform this action.", "forbidden": "Access denied.", "notFound": "The requested resource was not found.", "validation": "Please check your input and try again.", "serverError": "Server error. Please try again later.", "timeout": "Request timed out. Please try again.", "rateLimited": "Too many requests. Please wait and try again.", "maintenance": "System is under maintenance. Please try again later."}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "minLength": "Must be at least {{min}} characters", "maxLength": "Must be no more than {{max}} characters", "min": "Must be at least {{min}}", "max": "Must be no more than {{max}}", "pattern": "Invalid format", "url": "Please enter a valid URL", "number": "Please enter a valid number", "integer": "Please enter a valid integer", "positive": "Must be a positive number", "negative": "Must be a negative number", "date": "Please enter a valid date", "time": "Please enter a valid time", "phone": "Please enter a valid phone number", "password": "Password must contain at least 8 characters with uppercase, lowercase, and numbers", "confirmPassword": "Passwords do not match", "unique": "This value already exists", "fileSize": "File size must be less than {{size}}MB", "fileType": "Invalid file type. Allowed types: {{types}}"}}