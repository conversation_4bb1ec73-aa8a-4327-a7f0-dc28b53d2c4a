@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles */
@layer base {
  html {
    font-family: 'Inter', sans-serif;
  }
  
  html[dir="rtl"] {
    font-family: 'Cairo', sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100;
  }
  
  /* Scrollbar styles */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }
}

/* Component styles */
@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }
  
  .btn-warning {
    @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }
  
  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }
  
  .btn-outline {
    @apply btn border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-primary-500;
  }
  
  /* Card styles */
  .card {
    @apply bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-750;
  }
  
  /* Form styles */
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-primary-500 focus:border-primary-500;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1;
  }
  
  .form-error {
    @apply text-sm text-danger-600 dark:text-danger-400 mt-1;
  }
  
  /* Status badges */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800 dark:bg-success-800 dark:text-success-100;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800 dark:bg-warning-800 dark:text-warning-100;
  }
  
  .badge-danger {
    @apply badge bg-danger-100 text-danger-800 dark:bg-danger-800 dark:text-danger-100;
  }
  
  .badge-info {
    @apply badge bg-primary-100 text-primary-800 dark:bg-primary-800 dark:text-primary-100;
  }
  
  .badge-gray {
    @apply badge bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100;
  }
  
  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }
  
  /* Table styles */
  .table {
    @apply min-w-full divide-y divide-gray-200 dark:divide-gray-700;
  }
  
  .table-header {
    @apply bg-gray-50 dark:bg-gray-800;
  }
  
  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider;
  }
  
  .table-body {
    @apply bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700;
  }
  
  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100;
  }
  
  /* Sidebar styles */
  .sidebar {
    @apply fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out;
  }
  
  .sidebar-open {
    @apply translate-x-0;
  }
  
  .sidebar-closed {
    @apply -translate-x-full;
  }
  
  /* RTL support */
  [dir="rtl"] .sidebar {
    @apply left-auto right-0;
  }
  
  [dir="rtl"] .sidebar-closed {
    @apply translate-x-full;
  }
  
  /* Navigation styles */
  .nav-item {
    @apply flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200;
  }
  
  .nav-item-active {
    @apply nav-item bg-primary-100 text-primary-900 dark:bg-primary-900 dark:text-primary-100;
  }
  
  .nav-item-inactive {
    @apply nav-item text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white;
  }
}

/* Utility classes */
@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }
  
  .animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
  }
  
  /* Responsive text */
  .text-responsive {
    @apply text-sm sm:text-base md:text-lg lg:text-xl;
  }
  
  /* Glass effect */
  .glass {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  .glass-dark {
    backdrop-filter: blur(10px);
    background: rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* Dark mode specific styles */
@media (prefers-color-scheme: dark) {
  .dark-auto {
    @apply dark:bg-gray-900 dark:text-gray-100;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .btn {
    @apply border-2;
  }
  
  .card {
    @apply border-2;
  }
}
