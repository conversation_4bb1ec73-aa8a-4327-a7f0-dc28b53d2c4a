"""
Configuration settings for Facebook Marketplace Platform
إعدادات منصة فيسبوك ماركت بليس
"""

from pydantic_settings import BaseSettings
from typing import List, Optional
import os
from pathlib import Path


class Settings(BaseSettings):
    """Application settings"""
    
    # Basic settings
    PROJECT_NAME: str = "Facebook Marketplace Desktop Application"
    PROJECT_NAME_AR: str = "تطبيق فيسبوك ماركت بليس"
    VERSION: str = "1.0.0"
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # Security
    SECRET_KEY: str = "your-super-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    ALGORITHM: str = "HS256"
    
    # Database
    DATABASE_URL: str = "postgresql://postgres:postgres123@localhost:5432/facebook_marketplace"
    DATABASE_URL_ASYNC: Optional[str] = None
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379"
    REDIS_PASSWORD: Optional[str] = None
    
    # CORS
    ALLOWED_HOSTS: List[str] = ["*"]
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:8000",
        "http://127.0.0.1:8000"
    ]
    
    # File storage
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_FILE_TYPES: List[str] = ["jpg", "jpeg", "png", "gif", "webp"]
    
    # Browser automation
    BROWSER_PROFILES_DIR: str = "browser_profiles"
    BROWSER_TIMEOUT: int = 30000  # 30 seconds
    BROWSER_HEADLESS: bool = True
    BROWSER_SLOW_MO: int = 100  # milliseconds
    
    # Anti-ban settings
    MIN_DELAY: int = 5  # seconds
    MAX_DELAY: int = 15  # seconds
    HUMAN_TYPING_SPEED: int = 100  # milliseconds per character
    MOUSE_MOVEMENT_SPEED: int = 1000  # milliseconds
    
    # Facebook settings
    FB_LOGIN_URL: str = "https://www.facebook.com/login"
    FB_MARKETPLACE_URL: str = "https://www.facebook.com/marketplace"
    FB_MAX_DAILY_POSTS: int = 50
    FB_MAX_HOURLY_POSTS: int = 5
    
    # AI Content Generation
    OPENAI_API_KEY: Optional[str] = None
    ANTHROPIC_API_KEY: Optional[str] = None
    AI_MODEL: str = "gpt-3.5-turbo"
    AI_MAX_TOKENS: int = 1000
    AI_TEMPERATURE: float = 0.7
    
    # Email settings
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: int = 587
    SMTP_USERNAME: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    SMTP_TLS: bool = True
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    LOG_ROTATION: str = "1 day"
    LOG_RETENTION: str = "30 days"
    
    # Celery
    CELERY_BROKER_URL: str = "redis://localhost:6379/0"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/0"
    CELERY_TASK_SERIALIZER: str = "json"
    CELERY_RESULT_SERIALIZER: str = "json"
    CELERY_ACCEPT_CONTENT: List[str] = ["json"]
    CELERY_TIMEZONE: str = "UTC"
    CELERY_ENABLE_UTC: bool = True
    
    # Subscription plans
    STARTER_PLAN_ACCOUNTS: int = 5
    STARTER_PLAN_POSTS: int = 100
    PROFESSIONAL_PLAN_ACCOUNTS: int = 25
    PROFESSIONAL_PLAN_POSTS: int = 1000
    ENTERPRISE_PLAN_ACCOUNTS: int = -1  # Unlimited
    ENTERPRISE_PLAN_POSTS: int = -1  # Unlimited
    
    # Rate limiting
    RATE_LIMIT_PER_MINUTE: int = 60
    RATE_LIMIT_PER_HOUR: int = 1000
    RATE_LIMIT_PER_DAY: int = 10000
    
    # Monitoring
    SENTRY_DSN: Optional[str] = None
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090
    
    # Internationalization
    DEFAULT_LANGUAGE: str = "en"
    SUPPORTED_LANGUAGES: List[str] = ["en", "ar"]
    TIMEZONE: str = "UTC"
    
    @property
    def database_url_async(self) -> str:
        """Get async database URL"""
        if self.DATABASE_URL_ASYNC:
            return self.DATABASE_URL_ASYNC
        return self.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")
    
    def get_upload_path(self) -> Path:
        """Get upload directory path"""
        path = Path(self.UPLOAD_DIR)
        path.mkdir(exist_ok=True)
        return path
    
    def get_browser_profiles_path(self) -> Path:
        """Get browser profiles directory path"""
        path = Path(self.BROWSER_PROFILES_DIR)
        path.mkdir(exist_ok=True)
        return path
    
    def get_logs_path(self) -> Path:
        """Get logs directory path"""
        path = Path("logs")
        path.mkdir(exist_ok=True)
        return path
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# Create settings instance
settings = Settings()


# Environment-specific configurations
if settings.ENVIRONMENT == "production":
    settings.DEBUG = False
    settings.BROWSER_HEADLESS = True
    settings.LOG_LEVEL = "WARNING"
elif settings.ENVIRONMENT == "testing":
    settings.DATABASE_URL = "sqlite:///./test.db"
    settings.REDIS_URL = "redis://localhost:6379/1"
    settings.DEBUG = True
