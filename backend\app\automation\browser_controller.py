"""
Browser Controller for managing browser sessions
وحدة التحكم بالمتصفح لإدارة جلسات المتصفح
"""

import asyncio
import json
import os
import random
import uuid
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>rowserContext, Page
from playwright_stealth import stealth_async

from app.core.config import settings
from app.core.security import decrypt_sensitive_data
from app.models.account import FacebookAccount
from app.models.session import BrowserSession, SessionStatus, BrowserType

logger = logging.getLogger(__name__)


class BrowserController:
    """
    Browser Controller for managing browser sessions
    وحدة التحكم بالمتصفح لإدارة جلسات المتصفح
    """
    
    def __init__(self):
        self.playwright = None
        self.browsers: Dict[str, Browser] = {}
        self.contexts: Dict[str, BrowserContext] = {}
        self.pages: Dict[str, Page] = {}
        self.sessions: Dict[str, BrowserSession] = {}
        
    async def initialize(self):
        """Initialize Playwright"""
        if not self.playwright:
            self.playwright = await async_playwright().start()
            logger.info("Browser controller initialized")
    
    async def cleanup(self):
        """Cleanup all browser resources"""
        try:
            # Close all pages
            for page in self.pages.values():
                if not page.is_closed():
                    await page.close()
            
            # Close all contexts
            for context in self.contexts.values():
                await context.close()
            
            # Close all browsers
            for browser in self.browsers.values():
                await browser.close()
            
            # Stop playwright
            if self.playwright:
                await self.playwright.stop()
            
            # Clear dictionaries
            self.pages.clear()
            self.contexts.clear()
            self.browsers.clear()
            self.sessions.clear()
            
            logger.info("Browser controller cleaned up")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    async def create_session(
        self,
        facebook_account: FacebookAccount,
        session_config: Optional[Dict] = None
    ) -> str:
        """
        Create new browser session
        إنشاء جلسة متصفح جديدة
        """
        try:
            await self.initialize()
            
            session_id = str(uuid.uuid4())
            
            # Create session configuration
            config = self._get_session_config(facebook_account, session_config)
            
            # Launch browser
            browser = await self._launch_browser(config)
            self.browsers[session_id] = browser
            
            # Create context
            context = await self._create_context(browser, config)
            self.contexts[session_id] = context
            
            # Create page
            page = await context.new_page()
            self.pages[session_id] = page
            
            # Apply stealth mode
            if config.get("stealth_mode", True):
                await stealth_async(page)
            
            # Create session record
            session = BrowserSession(
                id=session_id,
                facebook_account_id=facebook_account.id,
                session_name=f"Session_{facebook_account.account_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                browser_type=config.get("browser_type", BrowserType.CHROMIUM.value),
                user_agent=config.get("user_agent"),
                viewport_width=config.get("viewport_width", 1920),
                viewport_height=config.get("viewport_height", 1080),
                headless=config.get("headless", True),
                proxy_server=config.get("proxy_server"),
                stealth_mode=config.get("stealth_mode", True),
                timeout_seconds=config.get("timeout", 30),
                slow_mo_milliseconds=config.get("slow_mo", 100)
            )
            
            session.start_session()
            self.sessions[session_id] = session
            
            logger.info(f"Browser session created: {session_id} for account {facebook_account.email}")
            
            return session_id
            
        except Exception as e:
            logger.error(f"Error creating browser session: {e}")
            raise
    
    async def get_session_status(self, session_id: str) -> Optional[SessionStatus]:
        """
        Get session status
        الحصول على حالة الجلسة
        """
        session = self.sessions.get(session_id)
        if not session:
            return None
        
        # Check if browser resources are still available
        if session_id not in self.pages or self.pages[session_id].is_closed():
            session.status = SessionStatus.TERMINATED.value
            return SessionStatus.TERMINATED
        
        return SessionStatus(session.status)
    
    async def terminate_session(self, session_id: str):
        """
        Terminate browser session
        إنهاء جلسة المتصفح
        """
        try:
            # Close page
            if session_id in self.pages:
                page = self.pages[session_id]
                if not page.is_closed():
                    await page.close()
                del self.pages[session_id]
            
            # Close context
            if session_id in self.contexts:
                context = self.contexts[session_id]
                await context.close()
                del self.contexts[session_id]
            
            # Close browser
            if session_id in self.browsers:
                browser = self.browsers[session_id]
                await browser.close()
                del self.browsers[session_id]
            
            # Update session status
            if session_id in self.sessions:
                session = self.sessions[session_id]
                session.end_session()
                del self.sessions[session_id]
            
            logger.info(f"Browser session terminated: {session_id}")
            
        except Exception as e:
            logger.error(f"Error terminating session {session_id}: {e}")
    
    async def get_page(self, session_id: str) -> Optional[Page]:
        """Get page for session"""
        return self.pages.get(session_id)
    
    async def navigate_to_url(self, session_id: str, url: str) -> bool:
        """
        Navigate to URL
        الانتقال إلى رابط
        """
        try:
            page = self.pages.get(session_id)
            if not page:
                return False
            
            # Add random delay
            await self._random_delay(1, 3)
            
            # Navigate to URL
            response = await page.goto(url, wait_until="networkidle")
            
            # Update session activity
            session = self.sessions.get(session_id)
            if session:
                session.record_page_view(url)
            
            return response.ok if response else False
            
        except Exception as e:
            logger.error(f"Error navigating to {url}: {e}")
            return False
    
    async def login_to_facebook(
        self,
        session_id: str,
        facebook_account: FacebookAccount
    ) -> bool:
        """
        Login to Facebook
        تسجيل الدخول إلى فيسبوك
        """
        try:
            page = self.pages.get(session_id)
            if not page:
                return False
            
            # Navigate to Facebook login
            await self.navigate_to_url(session_id, settings.FB_LOGIN_URL)
            
            # Wait for login form
            await page.wait_for_selector('input[name="email"]', timeout=10000)
            
            # Enter email
            await self._human_type(page, 'input[name="email"]', facebook_account.email)
            await self._random_delay(1, 2)
            
            # Enter password
            if facebook_account.encrypted_password:
                password = decrypt_sensitive_data(facebook_account.encrypted_password)
                await self._human_type(page, 'input[name="pass"]', password)
                await self._random_delay(1, 2)
            
            # Click login button
            await page.click('button[name="login"]')
            
            # Wait for navigation
            await page.wait_for_load_state("networkidle", timeout=30000)
            
            # Check if login was successful
            current_url = page.url
            if "facebook.com" in current_url and "login" not in current_url:
                facebook_account.update_last_login()
                logger.info(f"Successfully logged in to Facebook: {facebook_account.email}")
                return True
            else:
                logger.warning(f"Facebook login failed: {facebook_account.email}")
                return False
                
        except Exception as e:
            logger.error(f"Error logging in to Facebook: {e}")
            return False
    
    async def save_session_data(self, session_id: str) -> Dict:
        """
        Save session data (cookies, storage)
        حفظ بيانات الجلسة
        """
        try:
            context = self.contexts.get(session_id)
            page = self.pages.get(session_id)
            
            if not context or not page:
                return {}
            
            # Get cookies
            cookies = await context.cookies()
            
            # Get local storage
            local_storage = await page.evaluate("""
                () => {
                    const storage = {};
                    for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        storage[key] = localStorage.getItem(key);
                    }
                    return storage;
                }
            """)
            
            # Get session storage
            session_storage = await page.evaluate("""
                () => {
                    const storage = {};
                    for (let i = 0; i < sessionStorage.length; i++) {
                        const key = sessionStorage.key(i);
                        storage[key] = sessionStorage.getItem(key);
                    }
                    return storage;
                }
            """)
            
            session_data = {
                "cookies": cookies,
                "local_storage": local_storage,
                "session_storage": session_storage,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Update session record
            session = self.sessions.get(session_id)
            if session:
                session.save_cookies(cookies)
                session.save_local_storage(local_storage)
                session.save_session_storage(session_storage)
            
            return session_data
            
        except Exception as e:
            logger.error(f"Error saving session data: {e}")
            return {}
    
    async def load_session_data(self, session_id: str, session_data: Dict):
        """
        Load session data
        تحميل بيانات الجلسة
        """
        try:
            context = self.contexts.get(session_id)
            page = self.pages.get(session_id)
            
            if not context or not page:
                return
            
            # Load cookies
            if "cookies" in session_data:
                await context.add_cookies(session_data["cookies"])
            
            # Load local storage
            if "local_storage" in session_data:
                await page.evaluate(f"""
                    (storage) => {{
                        for (const [key, value] of Object.entries(storage)) {{
                            localStorage.setItem(key, value);
                        }}
                    }}
                """, session_data["local_storage"])
            
            # Load session storage
            if "session_storage" in session_data:
                await page.evaluate(f"""
                    (storage) => {{
                        for (const [key, value] of Object.entries(storage)) {{
                            sessionStorage.setItem(key, value);
                        }}
                    }}
                """, session_data["session_storage"])
            
            logger.info(f"Session data loaded for session: {session_id}")
            
        except Exception as e:
            logger.error(f"Error loading session data: {e}")
    
    def _get_session_config(
        self,
        facebook_account: FacebookAccount,
        custom_config: Optional[Dict] = None
    ) -> Dict:
        """Get session configuration"""
        config = {
            "browser_type": BrowserType.CHROMIUM.value,
            "headless": settings.BROWSER_HEADLESS,
            "timeout": settings.BROWSER_TIMEOUT,
            "slow_mo": settings.BROWSER_SLOW_MO,
            "viewport_width": 1920,
            "viewport_height": 1080,
            "stealth_mode": True,
            "user_agent": self._get_random_user_agent(),
        }
        
        # Add proxy if configured
        if facebook_account.proxy_settings:
            config.update(facebook_account.proxy_settings)
        
        # Override with custom config
        if custom_config:
            config.update(custom_config)
        
        return config
    
    async def _launch_browser(self, config: Dict) -> Browser:
        """Launch browser with configuration"""
        browser_type = config.get("browser_type", BrowserType.CHROMIUM.value)
        
        launch_options = {
            "headless": config.get("headless", True),
            "slow_mo": config.get("slow_mo", 100),
            "args": [
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-dev-shm-usage",
                "--disable-accelerated-2d-canvas",
                "--no-first-run",
                "--no-zygote",
                "--disable-gpu",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
            ]
        }
        
        # Add proxy if configured
        if config.get("proxy_server"):
            launch_options["proxy"] = {
                "server": config["proxy_server"],
                "username": config.get("proxy_username"),
                "password": config.get("proxy_password")
            }
        
        if browser_type == BrowserType.CHROMIUM.value:
            return await self.playwright.chromium.launch(**launch_options)
        elif browser_type == BrowserType.FIREFOX.value:
            return await self.playwright.firefox.launch(**launch_options)
        elif browser_type == BrowserType.WEBKIT.value:
            return await self.playwright.webkit.launch(**launch_options)
        else:
            return await self.playwright.chromium.launch(**launch_options)
    
    async def _create_context(self, browser: Browser, config: Dict) -> BrowserContext:
        """Create browser context"""
        context_options = {
            "viewport": {
                "width": config.get("viewport_width", 1920),
                "height": config.get("viewport_height", 1080)
            },
            "user_agent": config.get("user_agent"),
            "locale": "en-US",
            "timezone_id": "America/New_York",
        }
        
        return await browser.new_context(**context_options)
    
    def _get_random_user_agent(self) -> str:
        """Get random user agent"""
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]
        return random.choice(user_agents)
    
    async def _human_type(self, page: Page, selector: str, text: str):
        """Type text with human-like behavior"""
        await page.click(selector)
        await page.fill(selector, "")  # Clear field first
        
        for char in text:
            await page.type(selector, char, delay=random.randint(50, 150))
    
    async def _random_delay(self, min_seconds: float = 1, max_seconds: float = 3):
        """Add random delay"""
        delay = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(delay)


# Global browser controller instance
browser_controller = BrowserController()
