"""
Campaign model for managing posting campaigns
نموذج الحملات لإدارة حملات النشر
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, JSON, Float
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid
import enum
from datetime import datetime, timedelta

from app.core.database import Base


class CampaignStatus(str, enum.Enum):
    """Campaign status"""
    DRAFT = "draft"
    SCHEDULED = "scheduled"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    FAILED = "failed"


class CampaignType(str, enum.Enum):
    """Campaign type"""
    SINGLE_POST = "single_post"
    BULK_POST = "bulk_post"
    SCHEDULED_POST = "scheduled_post"
    RECURRING_POST = "recurring_post"
    A_B_TEST = "a_b_test"


class Campaign(Base):
    """Campaign model"""
    __tablename__ = "campaigns"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Foreign keys
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    template_id = Column(UUID(as_uuid=True), ForeignKey("templates.id"), nullable=True, index=True)
    
    # Campaign basic info
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    campaign_type = Column(String(50), default=CampaignType.SINGLE_POST.value, nullable=False)
    status = Column(String(50), default=CampaignStatus.DRAFT.value, nullable=False)
    
    # Scheduling
    start_date = Column(DateTime(timezone=True), nullable=True)
    end_date = Column(DateTime(timezone=True), nullable=True)
    timezone = Column(String(50), default="UTC", nullable=False)
    
    # Posting schedule
    posting_schedule = Column(JSON, nullable=True)  # Complex scheduling rules
    post_interval_minutes = Column(Integer, default=60, nullable=False)
    posts_per_day = Column(Integer, default=5, nullable=False)
    posts_per_hour = Column(Integer, default=1, nullable=False)
    
    # Target accounts
    target_facebook_accounts = Column(ARRAY(String), nullable=True)  # Account IDs
    use_all_accounts = Column(Boolean, default=False, nullable=False)
    
    # Content settings
    use_content_variations = Column(Boolean, default=True, nullable=False)
    randomize_posting_times = Column(Boolean, default=True, nullable=False)
    randomize_content = Column(Boolean, default=True, nullable=False)
    
    # Anti-ban settings
    min_delay_seconds = Column(Integer, default=300, nullable=False)  # 5 minutes
    max_delay_seconds = Column(Integer, default=1800, nullable=False)  # 30 minutes
    use_human_behavior = Column(Boolean, default=True, nullable=False)
    rotate_proxies = Column(Boolean, default=False, nullable=False)
    
    # Budget and limits
    max_posts = Column(Integer, nullable=True)  # Maximum posts for this campaign
    budget = Column(Float, nullable=True)  # Budget if applicable
    cost_per_post = Column(Float, default=0.0, nullable=False)
    
    # Progress tracking
    total_posts_planned = Column(Integer, default=0, nullable=False)
    total_posts_completed = Column(Integer, default=0, nullable=False)
    successful_posts = Column(Integer, default=0, nullable=False)
    failed_posts = Column(Integer, default=0, nullable=False)
    pending_posts = Column(Integer, default=0, nullable=False)
    
    # Performance metrics
    total_views = Column(Integer, default=0, nullable=False)
    total_clicks = Column(Integer, default=0, nullable=False)
    total_messages = Column(Integer, default=0, nullable=False)
    total_saves = Column(Integer, default=0, nullable=False)
    total_shares = Column(Integer, default=0, nullable=False)
    
    # A/B Testing (if applicable)
    is_ab_test = Column(Boolean, default=False, nullable=False)
    ab_test_variants = Column(JSON, nullable=True)
    ab_test_results = Column(JSON, nullable=True)
    
    # Error handling
    max_retries = Column(Integer, default=3, nullable=False)
    retry_delay_minutes = Column(Integer, default=30, nullable=False)
    stop_on_error = Column(Boolean, default=False, nullable=False)
    
    # Notifications
    notify_on_completion = Column(Boolean, default=True, nullable=False)
    notify_on_error = Column(Boolean, default=True, nullable=False)
    notification_email = Column(String(255), nullable=True)
    
    # Advanced settings
    custom_settings = Column(JSON, nullable=True)
    tags = Column(ARRAY(String), nullable=True)
    priority = Column(Integer, default=5, nullable=False)  # 1-10, 10 is highest
    
    # Execution details
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    last_execution_at = Column(DateTime(timezone=True), nullable=True)
    next_execution_at = Column(DateTime(timezone=True), nullable=True)
    
    # Notes and comments
    notes = Column(Text, nullable=True)
    execution_log = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="campaigns")
    template = relationship("Template", back_populates="campaigns")
    posts = relationship("Post", back_populates="campaign", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Campaign(id={self.id}, name={self.name}, status={self.status})>"
    
    @property
    def is_active(self) -> bool:
        """Check if campaign is active"""
        return self.status in [CampaignStatus.RUNNING.value, CampaignStatus.SCHEDULED.value]
    
    @property
    def is_completed(self) -> bool:
        """Check if campaign is completed"""
        return self.status == CampaignStatus.COMPLETED.value
    
    @property
    def is_running(self) -> bool:
        """Check if campaign is currently running"""
        return self.status == CampaignStatus.RUNNING.value
    
    @property
    def progress_percentage(self) -> float:
        """Calculate campaign progress percentage"""
        if self.total_posts_planned == 0:
            return 0.0
        return (self.total_posts_completed / self.total_posts_planned) * 100
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate"""
        if self.total_posts_completed == 0:
            return 0.0
        return (self.successful_posts / self.total_posts_completed) * 100
    
    @property
    def failure_rate(self) -> float:
        """Calculate failure rate"""
        if self.total_posts_completed == 0:
            return 0.0
        return (self.failed_posts / self.total_posts_completed) * 100
    
    @property
    def estimated_completion_time(self) -> datetime:
        """Estimate completion time based on current progress"""
        if self.total_posts_completed == 0 or not self.started_at:
            return None
        
        elapsed_time = datetime.utcnow() - self.started_at
        posts_remaining = self.total_posts_planned - self.total_posts_completed
        
        if posts_remaining <= 0:
            return datetime.utcnow()
        
        time_per_post = elapsed_time / self.total_posts_completed
        estimated_remaining_time = time_per_post * posts_remaining
        
        return datetime.utcnow() + estimated_remaining_time
    
    @property
    def total_cost(self) -> float:
        """Calculate total cost"""
        return self.total_posts_completed * self.cost_per_post
    
    def start_campaign(self):
        """Start the campaign"""
        self.status = CampaignStatus.RUNNING.value
        self.started_at = datetime.utcnow()
        if not self.next_execution_at:
            self.next_execution_at = datetime.utcnow()
    
    def pause_campaign(self):
        """Pause the campaign"""
        self.status = CampaignStatus.PAUSED.value
    
    def resume_campaign(self):
        """Resume the campaign"""
        self.status = CampaignStatus.RUNNING.value
    
    def complete_campaign(self):
        """Complete the campaign"""
        self.status = CampaignStatus.COMPLETED.value
        self.completed_at = datetime.utcnow()
    
    def cancel_campaign(self):
        """Cancel the campaign"""
        self.status = CampaignStatus.CANCELLED.value
        self.completed_at = datetime.utcnow()
    
    def fail_campaign(self, reason: str = None):
        """Mark campaign as failed"""
        self.status = CampaignStatus.FAILED.value
        self.completed_at = datetime.utcnow()
        if reason:
            self.add_log_entry("error", reason)
    
    def record_successful_post(self):
        """Record successful post"""
        self.successful_posts += 1
        self.total_posts_completed += 1
        self.pending_posts = max(0, self.pending_posts - 1)
        self.last_execution_at = datetime.utcnow()
        
        # Check if campaign is completed
        if self.total_posts_completed >= self.total_posts_planned:
            self.complete_campaign()
    
    def record_failed_post(self):
        """Record failed post"""
        self.failed_posts += 1
        self.total_posts_completed += 1
        self.pending_posts = max(0, self.pending_posts - 1)
        self.last_execution_at = datetime.utcnow()
        
        # Check if campaign should stop on error
        if self.stop_on_error:
            self.fail_campaign("Stopped due to error")
    
    def add_pending_post(self):
        """Add pending post"""
        self.pending_posts += 1
    
    def calculate_next_execution_time(self) -> datetime:
        """Calculate next execution time"""
        import random
        
        base_delay = self.post_interval_minutes * 60  # Convert to seconds
        
        if self.randomize_posting_times:
            # Add random variation (±25%)
            variation = base_delay * 0.25
            delay = base_delay + random.uniform(-variation, variation)
        else:
            delay = base_delay
        
        # Ensure delay is within min/max bounds
        delay = max(self.min_delay_seconds, min(self.max_delay_seconds, delay))
        
        return datetime.utcnow() + timedelta(seconds=delay)
    
    def update_next_execution_time(self):
        """Update next execution time"""
        self.next_execution_at = self.calculate_next_execution_time()
    
    def add_log_entry(self, level: str, message: str, details: dict = None):
        """Add log entry"""
        if not self.execution_log:
            self.execution_log = []
        
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": level,
            "message": message,
            "details": details or {}
        }
        
        self.execution_log.append(log_entry)
        
        # Keep only last 1000 log entries
        if len(self.execution_log) > 1000:
            self.execution_log = self.execution_log[-1000:]
    
    def get_target_accounts(self) -> list:
        """Get list of target Facebook account IDs"""
        if self.use_all_accounts:
            # Return all user's active Facebook accounts
            return [acc.id for acc in self.user.facebook_accounts if acc.is_active]
        else:
            return self.target_facebook_accounts or []
    
    def add_target_account(self, account_id: str):
        """Add target account"""
        if not self.target_facebook_accounts:
            self.target_facebook_accounts = []
        if account_id not in self.target_facebook_accounts:
            self.target_facebook_accounts.append(account_id)
    
    def remove_target_account(self, account_id: str):
        """Remove target account"""
        if self.target_facebook_accounts and account_id in self.target_facebook_accounts:
            self.target_facebook_accounts.remove(account_id)
